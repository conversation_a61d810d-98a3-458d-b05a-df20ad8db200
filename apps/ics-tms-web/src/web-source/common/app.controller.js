const buildInfo = require( '../common/constants/buildInfo' );

(function () {
  'use strict';

  angular.module('icsApp').controller('AppCtrl', AppCtrl);

  /*@ngInject*/
  function AppCtrl(
    $scope,
    $rootScope,
    $document,
    $state,
    Modernizr,
    screenSize,
    ngToast,
    authService,
    adminInstance,
    $transitions,
    featureFlags
  ) {
    let appFullView;
    const keycloakDomainName = localStorage.getItem('keycloakDomainName');

    $scope.globalMenuCollapse = globalMenuCollapse;
    $scope.searchToggle = searchToggle;
    $rootScope.parseErrorMessage = parseErrorMessage; // eslint-disable-line no-param-reassign
    $rootScope.printErrorMessage = printErrorMessage; // eslint-disable-line no-param-reassign

    $rootScope.logoutURI = `http://${keycloakDomainName}/auth/realms/ics-invenco/protocol/openid-connect/logout?id_token_hint=${localStorage.getItem(
      'idToken'
    )}`;

    $document.on('keydown', function (e) {
      const nodeName = e.target.nodeName.toLowerCase();
      if (
        e.which === 8 &&
        nodeName !== 'input' &&
        nodeName !== 'select' &&
        nodeName !== 'textarea'
      ) {
        // you can add others here inside brackets.
        e.preventDefault();
      }
    });

    moment.updateLocale('en', {
      calendar: {
        lastDay: '[Yesterday at] LT',
        sameDay: '[Today at] LT',
        nextDay: '[Tomorrow at] LT',
        lastWeek: 'll',
        nextWeek: 'dddd [at] LT',
        sameElse: 'll',
      },
    });

    moment.updateLocale('en', {
      relativeTime: {
        future: 'in %s',
        past: '%s ago',
        s: 'seconds',
        m: '%d minute',
        mm: '%d minutes',
        h: '%d hour',
        hh: '%d hours',
        d: '%d day',
        dd: '%d days',
        M: '%d month',
        MM: '%d months',
        y: '%d year',
        yy: '%d years',
      },
    });

    $scope.browser = {
      touch: Modernizr.touchevents,
    };

    $scope.buildVersion = buildInfo.branchName.startsWith( 'master' ) || buildInfo.branchName.startsWith( 'release' ) ? '' : buildInfo.buildVersion;

    $scope.adminInstance = {
      productName: adminInstance.productName,
      copyRightName: adminInstance.copyRightName,
      shortName: adminInstance.shortName,
    };

    $scope.desktop = screenSize.on('md, lg', function (match) {
      $scope.desktop = match;
    });

    $scope.mobile = screenSize.on('xs, sm', function (match) {
      $scope.mobile = match;
    });

    $scope.desktoplg = screenSize.on('lg', function (match) {
      $scope.desktoplg = match;
      hideSearchNav();
    });

    $scope.desktopmd = screenSize.on('md', function (match) {
      $scope.desktopmd = match;
    });

    $scope.desktopsm = screenSize.on('sm', function (match) {
      $scope.desktopsm = match;
    });

    $scope.mobilexs = screenSize.on('xs', function (match) {
      $scope.mobilexs = match;

      // If screen size match, close menu. This is used during window resizing.
      if (match) {
        closeGlobalMenu();
      } else {
        $scope.isGlobalMenuOpen = !appFullView;
      }
    });

    $transitions.onStart({}, function (transition) {
      const stateName = transition.to().name;

      // Do not show pages to unauthorized users
      function goTo404() {
        return transition.router.stateService.target('404');
      }

      // Redirect to Secure Reports, if the user is a bank user
      if ($rootScope.getToken() && authService.userHasRole('BANK_USER') && !['accountSettings','secure-reports','passwordSettings','mfaConfirm'].includes(stateName)) {
        return transition.router.stateService.target('secure-reports');
      }

      // Redirect to React App, SITE_DETAIL_V2 Migration
      if (stateName === 'site' && featureFlags.isOn('siteDetailV2')) {
        const params = transition.params();
        const siteId = params.id;
        window.history.pushState('', '', `/asset-management/sites/${siteId}`);
        return transition.router.stateService.target('main.assetManagement');
      }

      if (stateName === 'remote.downloads') {
        if (!authService.isAllowedAccess('GET_ROLLOUT')) {
          goTo404();
        }
      } else if (stateName === 'mediaSettings') {
        if (!authService.isAllowedAccess('VIEW_MEDIA_SETTINGS')) {
          goTo404();
        }
      } else if (
        stateName !== 'device-media' &&
        _.includes(stateName, 'media')
      ) {
        if (!authService.isAllowedAccess('GET_MEDIA')) {
          goTo404();
        }
      } else if (stateName === 'remote.library') {
        if (!authService.isAllowedAccess('GET_FILES')) {
          goTo404();
        }
      } else if (_.includes(stateName, 'rki')) {
        if (!authService.isAllowedAccess('GET_RKI')) {
          goTo404();
        }
      } else if (_.includes(stateName, 'companySettings')) {
        if (!authService.isAllowedAccess('VIEW_COMPANY_SETTINGS')) {
          goTo404();
        }
      } else if (
        stateName === 'remote.downloads.create' ||
        stateName === 'copyDownload'
      ) {
        if (!authService.isAllowedAccess('DELETE_ROLLOUT')) {
          goTo404();
        }
      }
    });

    $transitions.onSuccess({}, function (transition) {
      hideSearchNav();

      if (transition.to().name === 'remote.downloads.detail') {
        closeGlobalMenu();
      }
      appFullView = transition.to().data?.appView === 'view-is-full';

      // Close sidebar menu on state change
      if ($scope.mobilexs) {
        closeGlobalMenu();
      } else {
        $scope.isGlobalMenuOpen = !appFullView;
      }
    });

    function globalMenuCollapse() {
      $scope.isGlobalMenuOpen = !$scope.isGlobalMenuOpen;
    }

    function searchToggle() {
      $scope.showNavSearch = !$scope.showNavSearch;
    }

    function closeGlobalMenu() {
      $scope.isGlobalMenuOpen = false;
    }

    function hideSearchNav() {
      $scope.showNavSearch = false;
    }

    function parseErrorMessage(error, messagePrefix) {
      let errorMsg =
        messagePrefix && messagePrefix !== ''
          ? messagePrefix + ' failed: '
          : 'Failed: ';
      if (error.data) {
        errorMsg += error.data.message ? error.data.message : error.data;
      }
      return errorMsg;
    }

    function printErrorMessage(error, messagePrefix) {
      const errorMsg = $rootScope.parseErrorMessage(error, messagePrefix);
      ngToast.create({
        timeout: 7000,
        content: errorMsg,
      });
      return errorMsg;
    }
  }
})();
