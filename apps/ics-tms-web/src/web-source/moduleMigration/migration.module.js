import uiRouter from '@uirouter/angularjs';
import angular from 'angular'; // eslint-disable-line import/no-extraneous-dependencies

import CommonModule from '../common/common-module';

export default angular
  .module('migrationModule', [uiRouter, CommonModule])
  .config($stateProvider => {
    'ngInject';

        const states = [
            {
                name: 'playlist',
                url: '/playlist',
                parent: 'main',
                data: {
                    titleTag: 'Playlist',
                    navTitle: 'Playlists',
                    navMobileTitle: 'Playlist Root',
                    viewClass: 'view-is-centered'
                },
                template: '<div></div>'
            },
            {
                name: 'playlist_content',
                url: '/content',
                parent: 'playlist',
                data: {
                    titleTag: 'Playlist Content',
                    navTitle: 'Playlists',
                    navMobileTitle: 'Playlist Content',
                    viewClass: 'view-is-centered'
                },
                template: '<div></div>'
            },
            {
                name: 'playlist_builder',
                url: '/builder/:id',
                parent: 'playlist',
                data: {
                    titleTag: 'Playlist Builder',
                    navTitle: 'Playlists',
                    navMobileTitle: 'Playlist Builder',
                    viewClass: 'view-is-full',
                    appView: 'view-is-full'
                },
                template: '<div></div>'
            },
            {
                name: 'playlist_builder_new',
                url: '/builder',
                parent: 'playlist',
                data: {
                    titleTag: 'Playlist Builder',
                    navTitle: 'Playlists',
                    navMobileTitle: 'Playlist Builder',
                    viewClass: 'view-is-full',
                    appView: 'view-is-full'
                },
                template: '<div></div>'
            },
            {
                name: 'playlist_coupon',
                url: '/coupon',
                parent: 'playlist',
                data: {
                    titleTag: 'Coupon',
                    navTitle: 'Playlists',
                    navMobileTitle: 'Playlist Coupon',
                    viewClass: 'view-is-centered'
                },
                template: '<div></div>'
            },
            {
                name: 'playlist_coupon_multi',
                url: '/coupon/multi/:type',
                parent: 'playlist',
                data: {
                    titleTag: 'Multi Coupon',
                    navTitle: 'Playlists',
                    navMobileTitle: 'Playlist Multi Coupon',
                    viewClass: 'view-is-centered'
                },
                template: '<div></div>'
            },
            {
                parent: 'main',
                name: 'configManagement',
                url: '/remote/config-management{path:.*}',
                template: '<div></div>',
                data: {
                    titleTag: 'Configuration',
                    navTitle: 'Remote Management',
                    navMobileTitle: 'Remote Management',
                    viewClass: 'view-is-full',
                    appView: 'view-is-full'
                }
            },
            {
                parent: 'main',
                name: 'assetManagement',
                url: '/asset-management{path:.*}',
                template: '<div></div>',
                data: {
                    titleTag: 'Asset Management',
                    navTitle: 'Asset Management',
                    navMobileTitle: 'Asset Management',
                    viewClass: 'view-is-full',
                    appView: 'view-is-full'
                }
            },
            {
                parent: 'main',
                name: 'bulkEditAttributes',
                url: '/asset-management/sites/attributes/bulk-edit',
                template: '<div></div>',
                data: {
                    titleTag: 'Asset Management',
                    navTitle: 'Asset Management',
                    navMobileTitle: 'Asset Management',
                    viewClass: 'view-is-centered'
                }
            },
            {
                parent: 'main',
                name: 'importAttributes',
                url: '/asset-management/sites/attributes/import-attributes',
                template: '<div></div>',
                data: {
                    titleTag: 'Asset Management',
                    navTitle: 'Asset Management',
                    navMobileTitle: 'Asset Management',
                    viewClass: 'view-is-centered'
                }
            },
            {
                parent: 'main',
                name: 'importDevices',
                url: '/asset-management/sites/attributes/import-devices',
                template: '<div></div>',
                data: {
                    titleTag: 'Asset Management',
                    navTitle: 'Asset Management',
                    navMobileTitle: 'Asset Management',
                    viewClass: 'view-is-centered'
                }
            },
            {
                parent: 'main',
                name: 'pastImportDevices',
                url: '/asset-management/sites/attributes/import-devices/past-imports',
                template: '<div></div>',
                data: {
                    titleTag: 'Asset Management',
                    navTitle: 'Asset Management',
                    navMobileTitle: 'Asset Management',
                    viewClass: 'view-is-centered'
                }
            },
            {
                parent: 'main',
                name: 'importSiteTags',
                url: '/asset-management/sites/attributes/import-site-tags',
                template: '<div></div>',
                data: {
                    titleTag: 'Asset Management',
                    navTitle: 'Asset Management',
                    navMobileTitle: 'Asset Management',
                    viewClass: 'view-is-centered'
                }
            },
            {
                parent: 'main',
                name: 'pastImportSiteTags',
                url: '/asset-management/sites/attributes/import-site-tags/past-imports',
                template: '<div></div>',
                data: {
                    titleTag: 'Asset Management',
                    navTitle: 'Asset Management',
                    navMobileTitle: 'Asset Management',
                    viewClass: 'view-is-centered'
                }
            },
            {
                parent: 'main',
                name: 'deleteSiteTags',
                url: '/asset-management/sites/attributes/delete-site-tags',
                template: '<div></div>',
                data: {
                    titleTag: 'Asset Management',
                    navTitle: 'Asset Management',
                    navMobileTitle: 'Asset Management',
                    viewClass: 'view-is-centered'
                }
            },
            {
                parent: 'main',
                name: 'pastDeleteSiteTags',
                url: '/asset-management/sites/attributes/delete-site-tags/past-imports',
                template: '<div></div>',
                data: {
                    titleTag: 'Asset Management',
                    navTitle: 'Asset Management',
                    navMobileTitle: 'Asset Management',
                    viewClass: 'view-is-centered'
                }
            },
            {
                parent: 'main',
                name: 'reportManagement',
                url: '/report-management{path:.*}',
                template: '<div></div>',
                data: {
                    titleTag: 'Reports',
                    navTitle: 'Reports',
                    navMobileTitle: 'Reports',
                    viewClass: 'view-is-full',
                    appView: 'view-is-full'
                }
            },
            {
                parent: 'main',
                name: 'scheduleList',
                url: '/schedule-list{path:.*}',
                template: '<div></div>',
                data: {
                    titleTag: 'Schedule',
                    navTitle: 'Schedule',
                    navMobileTitle: 'Reports',
                    viewClass: 'view-is-full',
                    appView: 'view-is-full'
                }
            },
            {
                parent: 'main',
                name: 'fuelPriceManagement',
                url: '/fuel-price-management{path:.*}',
                template: '<div></div>',
                params: {
                    path: null
                },
                data: {
                    titleTag: 'Fuel Price Management',
                    navTitle: 'Fuel Price Management',
                    navMobileTitle: 'Fuel Price Management',
                    viewClass: 'view-is-full',
                    appView: 'view-is-full'
                }
            },
            {
                name: 'dashboard',
                url: '/dashboard',
                parent: 'main',
                template: '<div></div>',
                data: {
                    titleTag: 'Dashboard',
                    navTitle: 'Dashboard',
                    navMobileTitle: 'Dashboard',
                    viewClass: 'view-is-full',
                    appView: 'view-is-full'
                }
            },
            {
                name: 'signup',
                url: '/signup?token',
                template: '<div></div>',
                params: {},
                resolve: {},
            },
            {
                name: 'login',
                url: '/',
                template: '<div></div>',
            },
            {
                name: 'mfa',
                url: '/sessions/two-factor?redirect',
                template: '<div></div>',
                params: {},
                resolve: {}
            },
            {
                name: 'pass-reset',
                url: '/resetpassword?token',
                template: '<div></div>',
                params: {},
                resolve: {},
            },
            {
                name: 'pass-resend',
                url: '/resend-password',
                template: '<div></div>',
            },
            {
                name: 'pass-resend-sent',
                url: '/reset-email-sent',
                parent: 'pass-resend',
                template: '<div></div>',
                params: {},
                resolve: {},
            },
            {
                name: 'privacy',
                url: '/legal/privacy',
                template: '<div></div>',
            },
            {
                name: 'tos',
                url: '/legal/terms',
                template: '<div></div>',
            },
            {
                name: 'remote.packages',
                url: '/packages?pageIndex&pageSize&type&status&expanded',
                template: '<div></div>',
                data: {
                    titleTag: 'Offline Packages',
                    navTitle: 'Remote Management',
                    navMobileTitle: 'Remote Management',
                    viewClass: 'view-sticky-toolbar',
                },
            },
            {
                name: 'remote.packages.create',
                url: '/create',
                template: '<div></div>',
                data: {
                    titleTag: 'Create Offline Package',
                    navTitle: 'Create Package',
                    navMobileTitle: 'Create Package',
                    viewClass: 'view-is-centered',
                }
            },
            {
                name: 'companySettings',
                url: '/:company/settings',
                parent: 'main',
                template: '<div></div>',
                abstract: true,
                data: {
                    titleTag: 'Company Settings',
                    navTitle: 'Company Settings',
                    viewClass: 'view-is-full',
                    appView: 'view-is-full'
                }
            },
            {
                name: 'companySettings.people',
                url: '/people?q&size&page',
                template: '<div></div>',
                data: {
                    navMobileTitle: 'People'
                }
            },
            {
                name: 'addUser',
                parent: 'companySettings.people',
                url: '/add',
                views: {
                    '@companySettings': {
                        template: '<div></div>',
                    }
                },
                data: {
                    navMobileTitle: 'Add Person'
                },
            },
            {
                name: 'editUser',
                parent: 'companySettings.people',
                url: '/:id',
                views: {
                    '@companySettings': {
                        template: '<div></div>',
                    }
                },
                data: {
                    navMobileTitle: 'Edit Person'
                },
            },
            {
                name: 'companySettings.teams',
                url: '/teams',
                template: '<div></div>',
                data: {
                    navMobileTitle: 'Teams'
                },
            },
            {
                name: 'createTeam',
                parent: 'companySettings.teams',
                url: '/create',
                views: {
                    '@companySettings': {
                        template: '<div></div>',
                    }
                },
                data: {
                    navMobileTitle: 'Create New Team'
                }
            },
            {
                name: 'editTeam',
                parent: 'companySettings.teams',
                url: '/:id/edit',
                views: {
                    '@companySettings': {
                        template: '<div></div>',
                    }
                },
                data: {
                    navMobileTitle: 'Edit Team'
                },
            },
            {
                name: 'companySettings.siteGroups',
                url: '/site-groups',
                template: '<div></div>',
                data: {
                    navMobileTitle: 'Site Groups'
                },
            },
            {
                name: 'createSiteGroup',
                parent: 'companySettings.siteGroups',
                url: '/create',
                views: {
                    '@companySettings': {
                        template: '<div></div>',
                    }
                },
                data: {
                    navMobileTitle: 'Create Site Group'
                },
            },
            {
                name: 'editSiteGroup',
                parent: 'companySettings.siteGroups',
                url: '/:id/edit',
                views: {
                    '@companySettings': {
                        template: '<div></div>',
                    }
                },
                data: {
                    navMobileTitle: 'Edit Site Group'
                },
            },
            {
                name: 'companySettings.alarms',
                url: '/alarms',
                abstract: true,
                template: '<div></div>',
            },
            {
                name: 'alarmsList',
                parent: 'companySettings.alarms',
                url: '',
                template: '<div></div>',
                data: {
                    titleTag: 'Company Settings',
                    navTitle: 'Company Settings',
                    navMobileTitle: 'Settings'
                },
            },
            {
                name: 'addAlarm',
                parent: 'companySettings.alarms',
                url: '/add',
                template: '<div></div>',
                data: {
                    titleTag: 'Create Alarm Subscription',
                    navTitle: 'Create Alarm Subscription',
                    navMobileTitle: 'Create Alarm Subscription'
                },
            },
            {
                name: 'viewAlarm',
                parent: 'companySettings.alarms',
                url: '/:id',
                template: '<div></div>',
                data: {
                    titleTag: 'Subscription Details',
                    navTitle: 'Subscription Details',
                    navMobileTitle: 'Subscription Details'
                },
            },
            {
                name: 'companySettings.media',
                url: '/media',
                abstract: true,
                template: '<div></div>',
            },
            {
                name: 'mediaSettings',
                parent: 'companySettings.media',
                url: '',
                template: '<div></div>',
                data: {
                    navMobileTitle: 'Media'
                }
            },
            {
                name: 'companySettings.siteTagManagement',
                url: '/site-tags',
                template: '<div></div>',
                data: {
                    navMobileTitle: 'Site Tags'
                }
            },
            {
                name: 'remote.library',
                url: '/library?size&page&deviceType&name&filters',
                template: '<div></div>',
                data: {
                    titleTag: 'File Library',
                    navTitle: 'Remote Management',
                    navMobileTitle: 'Remote Management',
                    viewClass: 'view-is-centered'
                }
            },
            {
                name: 'secure-reports',
                url: '/secure-reports',
                template: '<div></div>',
                parent: 'main',
                data: {
                    titleTag: 'Secure Reports',
                    navTitle: 'Secure Reports',
                    navMobileTitle: 'Secure Reports',
                    viewClass: 'view-is-full',
                    appView: 'view-is-full'
                }
            },
            {
                name: 'remote.bulkOperations',
                url: '/bulk-operations?&page&size',
                template: '<div></div>',
                data: {
                    titleTag: 'Bulk Operations',
                    navTitle: 'Remote Management',
                    navMobileTitle: 'Remote Management',
                    viewClass: 'view-sticky-toolbar'
                },
            },
            { 
                name: 'mfaMerchantReset',
                parent: 'remote.bulkOperations',
                url: '/two-factor',
                template: '<div></div>',
            },
            {
                name: 'userSettings',
                url: '/settings',
                parent: 'main',
                template: '<div></div>',
                abstract: true,
                data: {
                  titleTag: 'Account Settings',
                  navTitle: 'Account Settings',
                  viewClass: 'view-is-center',
                  appView: 'view-is-center'
                }
              },
              {
                name: 'accountSettings',
                parent: 'userSettings',
                url: '/account',
                template: '<div></div>',
              },
              {
                name: 'passwordSettings',
                parent: 'userSettings',
                url: '/reset-password',
                template: '<div></div>',  
              },
              {
                name: 'releaseNotes',
                parent: 'main',
                url: '/release-notes',
                template: '<div></div>',
                data: {
                titleTag: 'Release Notes',
                },
               },
               {
                 name: 'licenses',
                 url: '/legal/licenses',
                 parent: 'main',
                 data: {
                 titleTag: 'Licenses',
                 navTitle: 'Licenses',
                 navMobileTitle: 'Licenses',
                 },
                 template: '<div></div>',
               },
               {
                 name: 'mfaConfirm',
                 parent: 'passwordSettings',
                 url: '/two-factor',
                 params: {
                   userInfo: null
                 },
                 template: '<div></div>',  
                 resolve: {
                  userInfo: ($stateParams) => {
                    'ngInject';
                     return $stateParams.userInfo;
                   }
                 }
               },
                {
                    name: 'notifications',
                    parent: 'main',
                    url: '/notifications',
                    template: '<div></div>',
                    data: {
                    titleTag: 'Notifications',
                    navTitle: 'Notifications',
                    navMobileTitle: 'Notifications',
                    viewClass: 'view-is-centered'
                    }
                },
                {
                  name: 'rki-list',
                  url: '/rki?size&page',
                  parent: 'main',
                  template: '<div></div>',
                  data: {
                    titleTag: 'Remote Key Injection',
                    navTitle: 'Remote Key Injection',
                    navMobileTitle: 'Remote Key Injection'
                  },
                },
                {
                  name: 'rki-create',
                  url: '/rki/create',
                  parent: 'main',
                  template: '<div></div>',
                  data: {
                    titleTag: 'RKI Create',
                    navTitle: 'RKI Create',
                    navMobileTitle: 'RKI Create',
                    viewClass: 'view-is-centered'
                  }
                },
                {
                  name: 'rki-details',
                  url: '/rki/:id',
                  parent: 'main',
                  template: '<div></div>',
                },
                {
                    name: 'sites-add',
                    url: '/sites/add',
                    parent: 'main',
                    template: '<div></div>',
                    data: {
                        titleTag: 'Add Site',
                        navTitle: 'Add Site',
                        navMobileTitle: 'Add Site',
                        viewClass: 'view-is-centered'
                    },
                },
                {
                    name: 'mediaLibrary',
                    parent: 'media',
                    url: '/library',
                    template: '<div></div>',
                    data: {
                        titleTag: 'Media Library',
                        navTitle: 'Media Library'
                    },
                 },
           ];

    return _.forEach(states, function (state) {
      $stateProvider.state(state);
    });
  }).name;
