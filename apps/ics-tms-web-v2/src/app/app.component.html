<app-login-header
  *ngIf="!(isLoggedIn$ | async)"
  class="bootstrap-iso"
></app-login-header>
<app-common-header
  *ngIf="isLoggedIn$ | async"
  (showNavSearchBar)="updateNavSearch($event)"
  [showNavSearch]="showNavSearch"
></app-common-header>
<section class="container-fluid">
  <div class="bootstrap-iso row">
    <app-side-nav
      *ngIf="(isLoggedIn$ | async) && (isSideBarOpen$ | async)"
    ></app-side-nav>
    <app-toast-container
      aria-live="polite"
      aria-atomic="true"
      [style]="'z-index: 1200'"
      [class]="'toast-container position-fixed bottom-0  start-0 p-3'"
    ></app-toast-container>
    <div
      [ngClass]="{
        'col-main': isLoggedIn$ | async,
        'mx-auto col-12': !(isLoggedIn$ | async),
      }"
    >
      <app-ics-loader *ngIf="isChunkLoading; else content"></app-ics-loader>
      <ng-template #content>
        <router-outlet></router-outlet>
      </ng-template>
    </div>
  </div>
</section>
<div
  aria-hidden="true"
  class="sidebar-blocker search-block"
  *ngIf="isNavbarChanged && showNavSearch && (isLoggedIn$ | async)"
  (click)="searchToggle()"
></div>
<div
  aria-hidden="true"
  class="sidebar-blocker"
  (click)="globalMenuCollapse()"
  *ngIf="isLoggedIn$ | async"
></div>
