import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatStepperModule } from '@angular/material/stepper';
import { MatTreeModule } from '@angular/material/tree';
import { RouterModule } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';

import {
  NgbCollapseModule,
  NgbDatepickerModule,
  NgbDropdown,
  NgbDropdownModule,
  NgbModule,
  NgbPagination,
  NgbProgressbarModule,
  NgbTimepickerModule,
  NgbTooltip,
} from '@ng-bootstrap/ng-bootstrap';

import { TimepickerModule } from 'ngx-bootstrap/timepicker';
import { InfiniteScrollDirective } from 'ngx-infinite-scroll';
import { NgOptionHighlightDirective } from '@ng-select/ng-option-highlight';
import { IcsHeaderTemplate1Component } from '../components/shared/ics-header-template1/ics-header-template1.component';
import { IcsLoaderComponent } from '../components/shared/ics-loader/ics-loader.component';
import { IcsMenuTemplateComponent } from '../components/shared/ics-menu-template/ics-menu-template.component';
import { IcsFirstColumnTemplateComponent } from '../components/shared/ics-table-skeleton/template-1/ics-first-column-template/ics-first-column-template.component';
import { Template1Component } from '../components/shared/ics-table-skeleton/template-1/template-1.component';
import { DateFormatPipe } from '../utils/date-format.pipe';

import { ChooseFileComponent } from '../components/shared/ics-stepper-template/choose-file/choose-file.component';
import { DownloadNameComponent } from '../components/shared/ics-stepper-template/download-name/download-name.component';
import { ScheduleComponent } from '../components/shared/ics-stepper-template/schedule/schedule.component';
import { SelectCompanyComponent } from '../components/shared/ics-stepper-template/select-company/select-company.component';
import { SelectActionComponent } from '../components/shared/ics-stepper-template/select-action/select-action.component';
import { SelectDevicesComponent } from '../components/shared/ics-stepper-template/select-devices/select-devices.component';
import { SelectDeviceModeComponent } from '../components/shared/ics-stepper-template/select-device-mode/select-device-mode.component';
import { IcsStepperTemplateComponent } from '../components/shared/ics-stepper-template/ics-stepper-template.component';
import { ApprovalComponent } from '../components/shared/ics-stepper-template/approval/approval.component';

import { IcsSiteGroupsMultiselectComponent } from '../components/shared/ics-multiselect-inputs/ics-site-groups-multiselect/ics-site-groups-multiselect.component';
import { IcsTagsMultiselectComponent } from '../components/shared/ics-multiselect-inputs/ics-tags-multiselect/ics-tags-multiselect.component';

import { CommonMultiSelectDropdownComponent } from '../components/shared/common-multi-select-dropdown/common-multi-select-dropdown.component';
import { IcsDropDownComponent } from '../components/shared/ics-drop-down/ics-drop-down/ics-drop-down.component';
import { IcsGrayTagsMultiselectComponent } from '../components/shared/ics-multiselect-inputs/ics-gray-tags-multiselect/ics-gray-tags-multiselect.component';
import { IcsPaginationComponent } from '../components/shared/ics-pagination/ics-pagination.component';
import { IcsSearchableDropdownComponent } from '../components/shared/ics-searchable-dropdown/ics-searchable-dropdown.component';
import { IcsSelectComponent } from '../components/shared/ics-select/ics-select.component';
import { IcsSortComponent } from '../components/shared/ics-sort/ics-sort.component';
import { IcsUserFilterComponent } from '../components/shared/ics-user-filter/ics-user-filter.component';

@NgModule({
  declarations: [
    DateFormatPipe,
    IcsLoaderComponent,
    IcsDropDownComponent,
    IcsHeaderTemplate1Component,
    IcsMenuTemplateComponent,
    IcsFirstColumnTemplateComponent,
    Template1Component,
    ChooseFileComponent,
    DownloadNameComponent,
    ScheduleComponent,
    SelectCompanyComponent,
    SelectActionComponent,
    SelectDevicesComponent,
    SelectDeviceModeComponent,
    IcsStepperTemplateComponent,
    IcsSiteGroupsMultiselectComponent,
    IcsTagsMultiselectComponent,
    IcsPaginationComponent,
    CommonMultiSelectDropdownComponent,
    IcsGrayTagsMultiselectComponent,
    IcsUserFilterComponent,
    IcsSelectComponent,
    IcsSortComponent,
    IcsSearchableDropdownComponent,
    ApprovalComponent,
  ],
  imports: [
    CommonModule,
    NgbModule,
    NgbProgressbarModule,
    NgbTooltip,
    NgbDropdown,
    NgSelectModule,
    MatStepperModule,
    MatIconModule,
    ReactiveFormsModule,
    NgbDatepickerModule,
    NgbTimepickerModule,
    FormsModule,
    NgbCollapseModule,
    NgbDropdownModule,
    MatTreeModule,
    MatCheckboxModule,
    RouterModule,
    InfiniteScrollDirective,
    TimepickerModule,
    NgOptionHighlightDirective,
  ],
  exports: [
    NgbPagination,
    NgbDatepickerModule,
    NgSelectModule,
    NgbCollapseModule,
    IcsLoaderComponent,
    IcsPaginationComponent,
    DateFormatPipe,
    IcsDropDownComponent,
    IcsHeaderTemplate1Component,
    IcsMenuTemplateComponent,
    IcsFirstColumnTemplateComponent,
    Template1Component,
    IcsSiteGroupsMultiselectComponent,
    IcsTagsMultiselectComponent,
    CommonMultiSelectDropdownComponent,
    IcsGrayTagsMultiselectComponent,
    IcsUserFilterComponent,
    IcsSelectComponent,
    IcsSortComponent,
    IcsSearchableDropdownComponent,
    NgOptionHighlightDirective,
  ],
})
export class SharedModuleModule {}
