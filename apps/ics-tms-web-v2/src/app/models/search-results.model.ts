/* eslint-disable @typescript-eslint/no-explicit-any */
export interface DevicesParams {
  [key: string]: any;
  pageIndex: number;
  pageSize: number;
  searchFilter?: string;
  showHiddenDevices: boolean;
}
export interface SitesParams {
  [key: string]: any;
  autoPoll: boolean;
  isCSV?: boolean;
  order?: string;
  pageIndex: number;
  pageSize: number;
  q?: string;
  showHiddenSites: boolean;
  tags?: string;
}
export interface SearchType {
  id: number;
  name: string;
  icon: string;
  placeholder: string;
}
export interface TagData {
  name: string;
  count: string;
}
