import { FeatureFlag } from './config.model';

export interface LoginResponse {
  fullName?: string;
  email?: string;
  password?: string | null;
  created?: string | number;
  userId?: string;
  roles?: string[] | null;
  company?: Company;
  flags?: Flags;
  token?: string | null;
  sub?: string;
  issuer?: string;
  otpURL?: string;
  qrCodeData?: string;
  csrf?: string;
  csrfHash?: string;
  secret?: {
    ascii: string;
    base32: string;
    hex: string;
  };
}
export interface Company {
  id: string;
  name: string;
  featureFlags?: string[] | null;
  sessionExpiryUserMins?: number;
}
export interface Flags {
  ADA: boolean;
  RENDITIONS: boolean;
}

export type GlobalState = {
  globalSidebarOpen: boolean;
  headerName: string;
  userDetails: LoginResponse;
  apiUrl: string;
  featureFlags: FeatureFlag[];
  isSuccess: boolean;
  isCompanyRuleCalled: boolean;
  captcha: {
    enabled: boolean;
    siteKey: string;
  };
  fileDownloadsLimits: {
    siteLimit: number;
    targetLimit: number;
  };
};

export interface ResetPasswordResponse {
  token: string;
}

export interface IsValidPasswordPayload {
  password: string;
  token: string;
  isReset: boolean;
}

export interface IsValidPasswordResponse {
  message: string;
}

export interface HttpError {
  status: number;
  message: string;
}

export interface LoginErrorResponse {
  status: number;
  message: string;
  failedStatus: boolean;
  isCaptchaEnabled: boolean;
  isLocked: boolean;
}

export interface DeviceType {
  id: string;
  name: string;
  screenWidth: number | null;
  screenHeight: number | null;
  featureflags: string[];
  scope: null | string;
}

export interface DeviceStatus {
  name: string;
  value: string;
}

export interface CommonResultMetadata {
  pageIndex: number;
  pageSize: number;
  totalResults: number;
}

export interface CommonResponseData<T> {
  resultsMetadata: CommonResultMetadata;
  results: T[];
}

export interface FilterItem<T> {
  group: string;
  value: string;
  selected: boolean;
  obj?: T;
}

export interface FilterGroup {
  name: string;
  options: Array<FilterItem<DeviceType | DeviceStatus>>;
}

export enum ViewType {
  FILE_DOWNLOADS = 'FILE_DOWNLOADS',
  MEDIA_DOWNLOADS = 'MEDIA_DOWNLOADS',
}

export type IcsSelectItem = {
  [key: string]: any;
  id: string | number;
  value: string;
};

export type IcsUser = {
  id: string;
  email: string;
  emailVerified: boolean;
  mfaConfigured: boolean;
  status: number;
  fullName: string;
  lastLocked: null;
  failedLoginAttempts: number;
  persona: string;
  company: {
    id: string;
    name: string;
    featureFlags: string[];
  };
  roles: string[];
  userGroups: Omit<UserGroup, 'usercount'>[];
  color?: string;
  letters?: string;
  isAdmin?: boolean;
  userGroupCount?: number;
  sub?: string;
};

export interface UserParams {
  allRoles?: boolean;
  pageIndex?: number;
  pageSize?: number;
  pending?: boolean;
  q?: string;
}

export interface UserGroup {
  usercount?: number;
  id: string;
  name: string;
}
export type Order = 'name' | 'created';

export interface UserGroupParams {
  [key: string]: any;
  pageIndex?: number;
  pageSize?: number;
  order?: Order;
  name?: string;
}

export interface UserRole {
  id: number;
  name: string;
  description: string;
  type?: string | null;
  isSelected?: boolean;
  isDisabled?: boolean;
  isRoleAvailable?: boolean;
}

export interface UserPersona {
  persona: string;
  roles: UserRole[];
}

export interface UserPersonasResponse {
  personas: UserPersona[];
  tenantId: string;
}

export interface ValidatePasswordResetPayload {
  userId: string;
  password: string;
}

export interface PaginationState {
  currentPage: number;
  pageSize: number;
  totalResults: number;
  isPaginationVisible: boolean;
}
