import { HttpClient } from '@angular/common/http';
import { Component, inject, OnInit, ViewEncapsulation } from '@angular/core';
import { Observable } from 'rxjs';
import { FILE_PATHS } from 'src/app/constants/appConstants';

@Component({
  selector: 'app-licenses',
  templateUrl: './licenses.component.html',
  styleUrls: ['./licenses.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class LicensesComponent implements OnInit {
  licenses: string[] = [];

  filePaths = FILE_PATHS;

  http = inject(HttpClient);

  ngOnInit() {
    this.filePaths.forEach(file => {
      this.loadLicense(file).subscribe((content: any) => {
        this.licenses.push(content);
      });
    });
  }

  loadLicense(path: string): Observable<string> {
    return this.http.get(path, { responseType: 'text' });
  }
}
