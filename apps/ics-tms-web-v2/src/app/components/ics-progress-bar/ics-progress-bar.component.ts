import { ChangeDetectorRef, Component, inject, OnInit } from '@angular/core';
import { AuthService } from 'src/app/services/auth.service';

@Component({
  selector: 'app-ics-progress-bar',
  templateUrl: './ics-progress-bar.component.html',
  styleUrls: ['./ics-progress-bar.component.scss'],
})
export class IcsProgressBarComponent implements OnInit {
  progress = 0;

  status = 0;

  timeoutId = null;

  authService = inject(AuthService);

  changeDetectorRef = inject(ChangeDetectorRef);

  ngOnInit() {
    this.authService.isLoading.subscribe(loading => {
      if (loading) {
        this.updateProgressBar(0.02);
        this.changeDetectorRef.detectChanges();
      } else {
        this.progress = 100;
        this.changeDetectorRef.detectChanges();
        setTimeout(() => {
          this.changeDetectorRef.detectChanges();
          this.resetProgressBar();
        }, 500);
      }
    });
  }

  updateProgressBar(n: number) {
    if (!this.authService.isLoading.value) return;
    this.progress = n * 100;
    this.status = n;
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
    setTimeout(() => {
      this.calculateIncrement();
      this.timeoutId = null;
    }, 250);
  }

  calculateIncrement() {
    if (this.status >= 1) return;
    let rnd = 0;
    const stat = this.status;
    if (stat < 0.99) {
      rnd = Math.max(0.005, Math.min(0.05, 0.15 - 0.15 * stat));
    } else rnd = 0;
    this.updateProgressBar(this.status + rnd);
  }

  resetProgressBar() {
    this.status = 0;
    this.progress = 0;
  }
}
