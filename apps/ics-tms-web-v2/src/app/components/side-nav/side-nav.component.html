<aside class="col-sidebar">
  <ul
    *ngFor="let app of navItems"
    class="nav nav-pills nav-stacked nav-pills-sidebar"
    [ngStyle]="{ display: app.isAllowed ? 'block' : 'none' }"
  >
    <li class="nav-pills-sidebar-header text-ellipsis" *ngIf="app.name">
      {{ app.name }}
    </li>
    <li
      *ngFor="let view of app.views"
      [ngStyle]="{ display: view.isAllowed ? 'block' : 'none' }"
      [ngClass]="[isActive(view) ? 'active' : '', view.customClass || '']"
      (click)="navigateTo(view.href)"
    >
      <a>
        <span class="icon" [ngClass]="view.icon"></span>
        <span class="text-ellipsis sidebar-app-title">{{ view.name }}</span>
      </a>
    </li>
  </ul>
</aside>
