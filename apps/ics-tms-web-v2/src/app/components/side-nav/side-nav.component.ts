import { Component, inject, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NavItem, View } from 'src/app/models/navItem.model';
import {
  getSideNavItems,
  isNavigationItemActive,
} from 'src/app/utils/side-nav.utils';

@Component({
  selector: 'app-side-nav',
  templateUrl: './side-nav.component.html',
  styleUrls: ['./side-nav.component.scss'],
})
export class SideNavComponent implements OnInit {
  navItems!: NavItem[];

  private router = inject(Router);

  ngOnInit(): void {
    this.navItems = getSideNavItems();
  }

  navigateTo(href: string) {
    this.router.navigateByUrl(href);
  }

  isActive(view: View): boolean {
    return isNavigationItemActive(view);
  }
}
