<app-ics-progress-bar></app-ics-progress-bar>
<nav class="navbar navbar-inverse navbar-fixed-top navbar-ics">
  <div class="container-fluid">
    <div class="row">
      <!-- Left Section: Menu Toggle + Header Name -->
      <div class="navbar-title-container col-xs-6 col-sm-6 col-md-3">
        <button
          type="button"
          (click)="toggleSidebar()"
          class="btn btn-circle navbar-btn pull-left"
        >
          <span class="sr-only">Toggle navigation</span>
          <span class="icon gicon-dehaze" aria-hidden="true"></span>
        </button>
        <div
          class="navbar-brand-text"
          *ngIf="!(headerName === 'Fuel Price Management' && isMobileView())"
        >
          {{ headerName }}
        </div>
      </div>

      <!-- Search Bar Section -->
      <div
        class="ics-navbar-global-q col-xs-12 col-md-6"
        [ngStyle]="{
          display: showNavSearch ? 'block' : 'none',
        }"
        *ngIf="!isBankUser()"
      >
        <app-search-bar class="app-search-bar"></app-search-bar>
      </div>

      <!-- Right Section -->
      <div class="ics-nav-right pull-right">
        <!-- Build Version (Optional) -->
        <div *ngIf="!isBankUser()" class="build-version-label">
          {{ buildVersion }}
        </div>

        <ul class="nav navbar-nav navbar-right">
          <!-- Responsive Search Icon -->
          <li class="visible-xs visible-sm" *ngIf="!isMobileView()">
            <button
              type="button"
              (click)="toggleSearch()"
              class="btn btn-link navbar-btn navbar-btn-icon no-focus"
            >
              <span class="icon gicon-search" aria-hidden="true"></span>
            </button>
          </li>

          <!-- Notification Bell -->
          <li *ngIf="!isBankUser()">
            <a
              class="pos-relative navbar-icon"
              [routerLink]="['/notifications']"
              [title]="notificationTooltip"
            >
              <span
                *ngIf="unreadCount > 0"
                class="icon gicon-notifications"
                aria-hidden="true"
              ></span>
              <span
                *ngIf="!unreadCount"
                class="icon gicon-notifications_none"
                aria-hidden="true"
              ></span>
              <strong
                *ngIf="unreadCount"
                class="notification-gem in-wobble"
                role="status"
                aria-live="polite"
              >
                {{ unreadCount }}
              </strong>
            </a>
          </li>

          <!-- User Dropdown -->
          <li
            ngbDropdown
            class="visible-xs visible-sm visible-md visible-lg"
            #userMenu="ngbDropdown"
          >
            <a
              id="user-settings-dropdown-nav"
              class="navbar-icon"
              role="button"
              ngbDropdownToggle
            >
              <span class="icon gicon-account_circle" aria-hidden="true"></span>
            </a>
            <ul
              ngbDropdownMenu
              class="ics-user-settings-dropdown"
              aria-labelledby="user-settings-dropdown-nav"
            >
              <li class="ics-user-card">
                <a
                  [routerLink]="['/settings/account']"
                  aria-label="Profile"
                  (click)="userMenu.close()"
                >
                  <div class="user-name text-ellipsis">{{ userName }}</div>
                </a>
                <div *ngIf="!isBankUser()" class="user-company text-ellipsis">
                  {{ companyName }}
                </div>
              </li>

              <li role="separator" class="divider" *ngIf="!isBankUser()"></li>
              <li>
                <a
                  href="/docs/index.html"
                  target="_blank"
                  aria-label="Developers"
                  (click)="userMenu.close()"
                  >Developers</a
                >
              </li>
              <li>
                <a
                  [routerLink]="['/release-notes']"
                  aria-label="Release notes"
                  (click)="userMenu.close()"
                  >Release notes</a
                >
              </li>
              <li>
                <a
                  [routerLink]="['/legal/licenses']"
                  aria-label="Licenses"
                  (click)="userMenu.close()"
                  >Licenses</a
                >
              </li>
              <li>
                <a (click)="downloadUserGuide()" aria-label="User guide"
                  >User Guide</a
                >
              </li>

              <li role="separator" class="divider"></li>
              <li>
                <a (click)="handleLogout()" (click)="userMenu.close()"
                  >Log out</a
                >
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  </div>
</nav>
