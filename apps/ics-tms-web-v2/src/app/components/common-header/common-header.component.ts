import {
  Component,
  EventEmitter,
  HostListener,
  inject,
  Input,
  NgZone,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { NgbDropdown } from '@ng-bootstrap/ng-bootstrap';
import { Subject, takeUntil } from 'rxjs';
import {
  closeSidebar,
  openSidebar,
} from 'src/app/store/actions/globalStore.actions';

import { NotificationParams } from 'src/app/modules/module-notifications/models/notification.model';
import { loadUnreadCount } from 'src/app/store/actions/unread-notifications.actions';
import {
  getLoginUserDetailsData,
  selectGlobalState,
} from 'src/app/store/selectors/globalStore.selectors';
import { selectUnreadCountData } from 'src/app/store/selectors/unread-notifications.selector';
import { AuthService } from 'src/app/services/auth.service';
import { RESOURCES } from 'src/app/constants/searchbarConstant';
import { UserService } from 'src/app/services/user.service';
import { BANK_USER } from 'src/app/modules/module-settings/constants/appConstants';
import { branchName } from 'src/constants/buildInfo';
import { GlobalState } from 'src/app/models/common';

@Component({
  selector: 'app-common-header',
  templateUrl: './common-header.component.html',
  styleUrls: ['./common-header.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class CommonHeaderComponent implements OnInit {
  @ViewChild('userMenu') userMenu!: NgbDropdown;

  private store = inject(Store);

  private userService = inject(UserService);

  private authService = inject(AuthService);

  private ngZone = inject(NgZone);

  @Input() showNavSearch = true;

  @Output() showNavSearchBar = new EventEmitter<boolean>();

  opened = true;

  headerName!: string;

  resources = RESOURCES;

  params: NotificationParams = {
    autoPoll: true,
    pageIndex: 0,
    pageSize: 20,
  };

  userName = '';

  companyName = '';

  unreadCount = 0;

  notificationTooltip = '0 Notifications';

  buildVersion =
    branchName.startsWith('master') || branchName.startsWith('release')
      ? ''
      : branchName;

  private notificationIntervalId: ReturnType<typeof setInterval> | null = null;

  private destroy$ = new Subject<void>();

  isBankUser = () => AuthService.hasRole(BANK_USER);

  ngOnInit(): void {
    this.handleSubscriptions();
    this.loadNotificationCount();
    this.unreadNotifications();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['showNavSearch']) {
      this.showNavSearch = changes['showNavSearch'].currentValue;
    }
  }

  ngOnDestroy(): void {
    if (this.notificationIntervalId) {
      clearInterval(this.notificationIntervalId);
    }

    this.destroy$.next();
    this.destroy$.complete();
  }

  handleSubscriptions() {
    this.store
      .select(getLoginUserDetailsData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        if (data.fullName && data.company?.name) {
          this.userName = data.fullName;
          this.companyName = data.company?.name;
        }
      });

    this.store
      .select(selectGlobalState)
      .pipe(takeUntil(this.destroy$))
      .subscribe((data: GlobalState) => {
        this.opened = data.globalSidebarOpen;
        this.headerName = data.headerName;
      });

    this.store
      .select(selectUnreadCountData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.unreadCount = data;
        this.notificationTooltip = `${this.unreadCount} Notification${
          this.unreadCount > 1 ? 's' : ''
        }`;
      });
  }

  toggleSearch(): void {
    this.showNavSearch = !this.showNavSearch;
    this.showNavSearchBar.emit(this.showNavSearch);
  }

  toggleSidebar() {
    this.opened
      ? this.store.dispatch(closeSidebar())
      : this.store.dispatch(openSidebar());
  }

  handleLogout() {
    this.authService.signOut();
  }

  unreadNotifications(): void {
    this.notificationIntervalId = setInterval(() => {
      this.loadNotificationCount();
    }, 60000);
  }

  loadNotificationCount() {
    this.store.dispatch(
      loadUnreadCount({
        params: { autoPoll: this.params.autoPoll },
      })
    );
  }

  downloadUserGuide() {
    this.closeUserMenu();
    this.userService.getUserGuide().subscribe();
  }

  closeUserMenu = () => {
    this.userMenu.close();
  };

  isMobileView() {
    return window.innerWidth < 768;
  }

  @HostListener('window:resize')
  onResize(): void {
    this.ngZone.run(() => {
      const isLargeScreen = window.innerWidth >= 992;
      this.showNavSearch = isLargeScreen;
    });
  }
}
