<div class="search-bar-container">
  <form
    class="global-search-form"
    (submit)="onSearchSubmit($event)"
    novalidate
    autocomplete="off"
    role="search"
  >
    <div
      class="input-group global-search"
      [ngClass]="{
        'has-focus': hasFocus || typeIsOpen || tagsIsOpen,
      }"
    >
      <!-- Search Type Dropdown -->
      <div class="input-group-btn">
        <div
          ngbDropdown
          class="btn-group btn-group-search-type"
          #searchTypeDropdown="ngbDropdown"
          (openChange)="typeIsOpen = $event"
        >
          <button
            id="searchTypeSelect"
            type="button"
            class="btn btn-global-search-type"
            ngbDropdownToggle
            aria-haspopup="true"
            aria-labelledby="searchTypeSelect"
          >
            {{ selectedItem.name }}
            <span class="caret"></span>
          </button>

          <ul
            ngbDropdownMenu
            class="dropdown-menu"
            role="menu"
            aria-labelledby="searchTypeSelect"
          >
            <li
              *ngFor="let item of searchTypes"
              class="btn-dropdown-link"
              (click)="selectItem(item); searchTypeDropdown.close()"
              [class.active]="item === selectedItem"
            >
              <span class="icon" [ngClass]="item.icon"></span>
              <span type="button" class="dropdown-item">
                {{ item.name }}
              </span>
            </li>
          </ul>
        </div>
      </div>

      <!-- Input Search (for types other than id=1) -->
      <div *ngIf="selectedItem.id !== 1" class="flex-grow-1">
        <label class="sr-only" for="search-query">Search query</label>
        <input
          #searchInput
          id="search-query"
          type="text"
          class="form-control form-control-search-q"
          [(ngModel)]="searchQuery"
          name="searchQuery"
          required
          [placeholder]="selectedItem.placeholder"
          (focus)="hasFocus = true"
          (blur)="hasFocus = false"
          (keydown)="onKeyDown($event)"
        />
      </div>

      <!-- Tag Multi-select (for id=1) -->
      <div *ngIf="selectedItem.id === 1" class="global-search-tags">
        <app-multi-select-tags
          [options]="(tags$ | async) ?? []"
          [selectedTags]="selectedTags"
          [isLoading]="(isLoading$ | async) ?? false"
          (selectionChange)="onTagSelection($event)"
          (updateInputFocus)="updateInputFocus($event)"
        ></app-multi-select-tags>

        <button
          type="submit"
          class="btn btn-primary btn-box-shadow btn-tags-search"
          [disabled]="!selectedTags.length"
        >
          Search
        </button>
      </div>
    </div>
  </form>
</div>
