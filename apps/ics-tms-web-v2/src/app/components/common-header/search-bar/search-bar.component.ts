import {
  Component,
  ElementRef,
  inject,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BehaviorSubject, filter, Observable, Subject, takeUntil } from 'rxjs';
import { SEARCH_TYPES } from 'src/app/constants/searchbarConstant';
import { SearchType } from 'src/app/models/search-results.model';
import { SearchTag, Tags } from 'src/app/models/tags.model';
import { loadTags } from 'src/app/store/actions/tags.actions';
import {
  selectTagsData,
  selectTagsLoading,
} from 'src/app/store/selectors/tags.selectors';

@Component({
  selector: 'app-search-bar',
  templateUrl: './search-bar.component.html',
  styleUrls: ['./search-bar.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class SearchBarComponent implements OnInit {
  @ViewChild('searchInput') searchInputRef?: ElementRef<HTMLInputElement>;

  searchTypes: SearchType[] = SEARCH_TYPES;

  selectedTags: SearchTag[] = [];

  selectedItem = SEARCH_TYPES[0];

  tags$ = new BehaviorSubject<SearchTag[]>([]);

  searchQuery = '';

  store = inject(Store);

  router = inject(Router);

  hasFocus = false;

  typeIsOpen = false;

  tagsIsOpen = false;

  isLoading$ = new Observable<boolean>();

  private destroy$ = new Subject<void>();

  ngOnInit() {
    this.handleSubscriptions();
  }

  handleSubscriptions() {
    this.store
      .select(selectTagsData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        const tagsData = data
          .filter((tag: Tags) => (tag?.siteCount ?? 0) > 0)
          .map((tag: Tags) => ({
            ...tag,
            siteCountString: `${tag.siteCount} site${
              (tag.siteCount ?? 0) > 1 ? 's' : ''
            }`,
          }));
        this.tags$.next(tagsData);
      });
    this.isLoading$ = this.store.select(selectTagsLoading);

    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.selectItem(SEARCH_TYPES[0], true);
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onTagSelection(tags: SearchTag[]) {
    this.selectedTags = tags;
  }

  onSearchSubmit(event: Event) {
    event.preventDefault();
    switch (this.selectedItem.id) {
      case 0:
        this.searchSite(this.searchQuery);
        break;
      case 1:
        this.searchByTags();
        break;
      case 2:
        this.searchDevice(this.searchQuery);
        break;
      default:
        break;
    }
  }

  private searchByTags() {
    this.router.navigate(['/asset-management/sites'], {
      queryParams: {
        tags: this.selectedTags.map(tag => tag.name).join(','),
      },
    });
  }

  private searchSite(query: string) {
    this.router.navigate(['/asset-management/sites'], {
      queryParams: {
        ...(query ? { q: query } : {}),
      },
    });
  }

  private searchDevice(query: string) {
    this.router.navigate(['/search'], {
      queryParams: {
        src: 'devices',
        page: 0,
        ...(query ? { q: query } : {}),
      },
    });
  }

  public selectItem(item: SearchType, isRouteChange = false) {
    setTimeout(() => {
      if (this.searchInputRef && !isRouteChange) {
        this.searchInputRef.nativeElement.focus();
      }
    });
    if (this.selectedItem.id === item.id) return;
    this.selectedItem = item;
    this.searchQuery = '';
    this.selectedTags = [];
    if (item.id === 1) {
      this.loadSiteTags();
    }
  }

  private loadSiteTags() {
    this.store.dispatch(loadTags());
  }

  public updateInputFocus(value: boolean) {
    this.hasFocus = value;
  }

  public onKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      this.onSearchSubmit(event);
    }
  }
}
