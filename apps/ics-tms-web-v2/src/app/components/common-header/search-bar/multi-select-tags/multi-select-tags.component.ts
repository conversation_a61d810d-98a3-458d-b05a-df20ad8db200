import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  Output,
  SimpleChanges,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { NgSelectComponent } from '@ng-select/ng-select';
import { SearchTag } from 'src/app/models/tags.model';

@Component({
  selector: 'app-multi-select-tags',
  templateUrl: './multi-select-tags.component.html',
  styleUrls: ['./multi-select-tags.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class MultiSelectTagsComponent {
  @Input() options: SearchTag[] = [];

  @Input() selectedTags: SearchTag[] = [];

  @Input() isLoading = false;

  @Output() selectionChange = new EventEmitter<SearchTag[]>();

  @Output() updateInputFocus = new EventEmitter<boolean>();

  @ViewChild('moreDropdownWrapper') moreDropdownWrapper!: ElementRef;

  public ngSelectClearFn!: (item: SearchTag) => void;

  skipOutsideClickOnce = false;

  showMoreDropdown = false;

  visibleTagCount = 2;

  hasFocus = false;

  ngAfterViewInit() {
    setTimeout(() => this.updateTagCountFromContainer(), 0);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isLoading']) {
      this.isLoading = changes['isLoading'].currentValue;

      if (changes['selectedTags']) {
        this.selectedTags = changes['selectedTags'].currentValue;
      }
    }
  }

  trackByTagId(tag: SearchTag): number {
    return tag.id;
  }

  handleMoreTags(event: Event, select: NgSelectComponent) {
    event.preventDefault();
    event.stopPropagation();
    select.close();
    this.skipOutsideClickOnce = true;
    this.showMoreDropdown = !this.showMoreDropdown;
  }

  @HostListener('window:resize')
  onWindowResize() {
    this.updateTagCountFromContainer();
  }

  private updateTagCountFromContainer() {
    const container = document.querySelector(
      '.ng-value-container'
    ) as HTMLElement;
    if (!container) return;

    const containerWidth = container.offsetWidth;
    const approxTagWidth = 100;
    this.visibleTagCount = Math.floor(containerWidth / approxTagWidth) - 2;
  }

  handleTagsDropdown(select: NgSelectComponent) {
    if (this.showMoreDropdown) this.showMoreDropdown = false;
    select.open();
  }

  get remainingTagCount(): number {
    return Math.max(this.selectedTags.length - this.visibleTagCount, 0);
  }

  @HostListener('document:click', ['$event'])
  handleClickOutside(event: MouseEvent) {
    if (this.skipOutsideClickOnce) {
      this.skipOutsideClickOnce = false;
      return;
    }
    if (
      this.showMoreDropdown &&
      this.moreDropdownWrapper &&
      !this.moreDropdownWrapper.nativeElement.contains(event.target)
    ) {
      this.showMoreDropdown = false;
    }
  }

  onMoreDropdownClick(event: MouseEvent) {
    event.stopPropagation();
    event.preventDefault();
  }

  setClearFn(clearFn: (item: any) => void): boolean {
    this.ngSelectClearFn = clearFn;
    return true;
  }

  onChange(selectedTags: SearchTag[]) {
    this.selectionChange.emit(selectedTags);
  }

  handleFocusChange(event: Event) {
    this.hasFocus = event.type === 'focus';
    this.updateInputFocus.emit(event.type === 'focus');
  }
}
