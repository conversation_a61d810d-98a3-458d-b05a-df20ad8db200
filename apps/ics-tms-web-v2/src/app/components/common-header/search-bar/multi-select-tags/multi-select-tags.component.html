<div class="multi-select-tags-container">
  <ng-select
    #tagSelect
    [items]="options"
    [(ngModel)]="selectedTags"
    [bindLabel]="'name'"
    (ngModelChange)="onChange($event)"
    [multiple]="true"
    [virtualScroll]="true"
    [searchable]="true"
    placeholder="Select tags"
    [trackByFn]="trackByTagId"
    [hideSelected]="true"
    [clearOnBackspace]="true"
    [loading]="isLoading"
    (open)="handleTagsDropdown(tagSelect)"
    (focus)="handleFocusChange($event)"
    (blur)="handleFocusChange($event)"
    class="ng-multi-select-container"
    [ngClass]="{ 'has-focus': hasFocus }"
  >
    <!-- Option template with site count -->
    <ng-template
      ng-option-tmp
      let-item="item"
      let-index="index"
      let-search="searchTerm"
    >
      <div class="ui-select-choices-row-inner">
        <span class="option-name" [ngOptionHighlight]="search">{{
          item.name
        }}</span>
        <span class="sites-count" *ngIf="item.siteCountString">{{
          item.siteCountString
        }}</span>
      </div>
    </ng-template>
    <!-- Loading state -->
    <ng-template ng-loadingspinner-tmp>
      <span class="fa-li fa fa-spinner fa-spin tag-loading"></span>
    </ng-template>

    <!-- Empty state -->
    <ng-template ng-notfound-tmp>
      <div class="text-center">
        <span>No tags available</span>
      </div>
    </ng-template>

    <ng-template ng-multi-label-tmp let-clear="clear">
      <ng-container *ngIf="setClearFn(clear)"></ng-container>
      <ng-container
        *ngFor="let item of selectedTags | slice: 0 : visibleTagCount"
      >
        <div
          class="ng-value search-tag-badge"
          (mousedown)="onMoreDropdownClick($event)"
        >
          <span class="ng-value-label truncate-label" [title]="item.name">{{
            item.name
          }}</span>
          <span
            class="ng-value-icon right"
            (click)="clear(item)"
            aria-hidden="true"
            >&times;</span
          >
        </div>
      </ng-container>
      <div
        class="ng-value more-tags-container search-tag-badge"
        *ngIf="remainingTagCount > 0"
        (mousedown)="handleMoreTags($event, tagSelect)"
      >
        <span class="ng-value-label">{{ remainingTagCount }} more...</span>
      </div>
    </ng-template>
  </ng-select>
  <div
    #moreDropdownWrapper
    class="more-tags-dropdown"
    [class.visible]="showMoreDropdown && selectedTags.length > visibleTagCount"
    (click)="onMoreDropdownClick($event)"
  >
    <div
      class="ng-value search-tag-badge"
      *ngFor="let tag of selectedTags.slice(visibleTagCount)"
    >
      <span class="ng-value-label truncate-label" [title]="tag.name">{{
        tag.name
      }}</span>
      <span class="ng-value-icon right" (mousedown)="ngSelectClearFn(tag)"
        >&times;</span
      >
    </div>
  </div>
</div>
