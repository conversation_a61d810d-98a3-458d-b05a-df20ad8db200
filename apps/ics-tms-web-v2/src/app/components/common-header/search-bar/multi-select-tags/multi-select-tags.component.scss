.multi-select-tags-container {
  .ng-multi-select-container {
    display: flex;
    align-items: center;
    height: 34px;
    padding: 5px 8px 2px;
    border: 0;
    background-color: transparent;

    &.has-focus {
      .ng-placeholder {
        color: var(--md-grey-700) !important;
      }
    }

    .ng-select-container {
      min-height: auto;
      height: 22px;
      margin-bottom: 3px;
      background: transparent !important;
      border: none;
      box-shadow: none !important;
      overflow: visible !important;
      .ng-clear-wrapper {
        display: none;
      }
      .ng-arrow-wrapper {
        display: none;
      }
      .ng-value-container {
        background: transparent;
        outline: none;
        padding: 0 !important;
        .ng-input {
          padding: 0 !important;
        }
        .ng-placeholder {
          top: 10% !important;
          color: var(--white);
        }
        .ng-value {
          &.more-tags-container {
            position: relative;
          }
        }
      }
      .tag-loading {
        left: 97%;
        color: var(--white);
      }
    }
    .ng-dropdown-panel {
      opacity: 1;
      padding: 5px 0;

      box-shadow:
        0 1px 3px 0 rgba(0, 0, 0, 0.2),
        0 1px 8px 0 rgba(0, 0, 0, 0.14),
        0 2px 1px -1px rgba(0, 0, 0, 0.12);
      top: 110%;
      .ng-dropdown-panel-items {
        .ng-option {
          .ui-select-choices-row-inner {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .option-name {
              .highlighted {
                text-decoration: none;
              }
            }
          }
          &.ng-option-marked {
            color: var(--white);
            background-color: #3f51b5;
          }
        }
      }
    }
  }
  .search-tag-badge {
    margin-bottom: 0 !important;
    font-weight: bold;
    padding: 0;
    color: #212121 !important;
    border-color: #e0e0e0;
    border-radius: 5em !important;
    background-color: #e0e0e0 !important;
    cursor: pointer;
    &:hover {
      color: #000 !important;
      background-color: #c1c1c1 !important;
      border-color: #c1c1c1;
    }
  }

  .truncate-label {
    display: inline-block;
    max-width: 10rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: bottom;
  }
  .ng-value-icon {
    font-size: 1.6em;
    font-weight: 400;
    line-height: 85%;
    opacity: 0.7;
    text-shadow: none;
    border: none !important;
    background-color: transparent !important;
  }
  .more-tags-dropdown {
    display: none;
    position: absolute;
    top: 110%;
    left: 0;
    z-index: 1000;
    background: #fff;
    border: 1px solid #ccc;
    padding: 6px 8px;
    border-radius: 4px;
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.2),
      0 1px 8px 0 rgba(0, 0, 0, 0.14),
      0 2px 1px -1px rgba(0, 0, 0, 0.12);
    min-width: 150px;
    white-space: nowrap;
    &.visible {
      display: flex;
      align-items: center;
      min-width: 25rem;
      flex-wrap: wrap;
      gap: 1rem;
      max-height: 15rem;
      overflow-y: auto;
    }
    .search-tag-badge {
      margin-right: 5px;
      font-size: 0.9em;
      white-space: nowrap;
      .ng-value-label {
        padding: 0.1rem 0.5rem;
      }
      .ng-value-icon {
        display: inline-block;
        padding: 0.1rem 0.5rem;
      }
    }
  }
}
