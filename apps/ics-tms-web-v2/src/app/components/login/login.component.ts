import {
  Component,
  ViewChild,
  ElementRef,
  AfterViewInit,
  Renderer2,
  ChangeDetectorRef,
} from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { combineLatest } from 'rxjs';
import { RecaptchaComponent } from 'ng-recaptcha';

import { LOGIN_USER, RESET_MFA } from '../../constants/appConstants';
import { ResetMFA } from '../../models/reset-mfa.model';
import { AuthService } from 'src/app/services/auth.service';
import { setLoginUserDetails } from 'src/app/store/actions/globalStore.actions';
import { GlobalState, LoginErrorResponse } from 'src/app/models/common';
import {
  captchSiteKey,
  isCaptchaEnabled,
} from 'src/app/store/selectors/globalStore.selectors';
import { ICaptcha } from 'src/app/models/login';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements AfterViewInit {
  @ViewChild('email') emailInput!: ElementRef;

  @ViewChild('password') passwordInput!: ElementRef;

  @ViewChild('reCaptcha') reCaptchaEle!: RecaptchaComponent;

  username = '';

  password = '';

  captchaResponse: string | null = null;

  noCaptchaSelected: boolean = false;

  showToolTip: boolean = false;

  isLoading: boolean = false;

  onFocusPasswordInfo() {
    this.showToolTip = true;
  }

  onBlurPasswordInfo() {
    this.showToolTip = false;
  }

  captcha: ICaptcha = {
    enabled: false,
    siteKey: '',
  };

  private readonly defaultError: LoginErrorResponse = {
    status: -1,
    message: '',
    failedStatus: false,
    isCaptchaEnabled: this.captcha?.enabled ?? false,
    isLocked: false,
  };

  error: LoginErrorResponse = this.defaultError;

  isAccountLocked = false;

  constructor(
    private authService: AuthService,
    private router: Router,
    private store: Store<GlobalState>,
    private renderer: Renderer2,
    private changeDetector: ChangeDetectorRef
  ) {
    combineLatest([
      store.select(isCaptchaEnabled),
      store.select(captchSiteKey),
    ]).subscribe(([isCaptchaEnabledValue, captchaSiteKeyValue]) => {
      this.captcha = {
        enabled: isCaptchaEnabledValue,
        siteKey: captchaSiteKeyValue,
      };
    });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      if (this.emailInput?.nativeElement) {
        this.renderer.selectRootElement(this.emailInput.nativeElement).focus();
      }
    });
  }

  onCaptchaResponse(e: string | null): void {
    this.captchaResponse = e;
  }

  resetLoginForm(): void {
    this.password = '';
  }

  handleLogin(valid: boolean | null | undefined): void {
    if (
      !valid ||
      (this.error?.failedStatus &&
        this.captcha &&
        this.captcha.enabled &&
        this.captcha.siteKey &&
        !this.captchaResponse)
    ) {
      this.password = '';
      this.noCaptchaSelected = !this.captchaResponse;
      this.error.status = -1;

      if (!valid && this.reCaptchaEle) {
        this.reCaptchaEle.reset();
      }
      return;
    }

    // reset error state
    this.error = this.defaultError;

    const payload = {
      email: this.username,
      password: this.password,
      ...(this.captcha?.enabled && this.captchaResponse
        ? { captchaResponse: this.captchaResponse }
        : {}),
    };

    this.isLoading = true;
    this.authService.authenticate(payload)?.subscribe({
      next: data => {
        // store user details
        this.store.dispatch(setLoginUserDetails({ userDetails: payload }));
        // set login status
        this.authService.isLogin.next(true);
        // route to 2 factor auth
        if (data) {
          this.authService.setTwoFactorVerificationType(RESET_MFA);
          this.authService.setResetMFA(data as ResetMFA);
        } else {
          this.authService.setTwoFactorVerificationType(LOGIN_USER);
        }
        this.router.navigate(['sessions', 'two-factor']);

        this.isLoading = false;
        this.noCaptchaSelected = false;
      },
      error: e => {
        const { status, error } = e;
        this.password = '';
        this.captchaResponse = null;
        this.noCaptchaSelected = false;
        this.error = {
          status: status ?? -1,
          message: error?.message ?? '',
          failedStatus: error?.failedStatus ?? false,
          isCaptchaEnabled: error?.isCaptchaEnabled ?? false,
          isLocked: error?.isLocked ?? false,
        };
        // manually detect the changes for reCaptchaEle
        this.changeDetector.detectChanges();
        if (
          error?.failedStatus &&
          error?.isCaptchaEnabled &&
          this.reCaptchaEle
        ) {
          this.reCaptchaEle.reset();
        }

        if (this.error?.isLocked) {
          this.isAccountLocked = true;
        }

        this.isLoading = false;
      },
    });
  }
}
