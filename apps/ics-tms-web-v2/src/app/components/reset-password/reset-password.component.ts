import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { debounceTime } from 'rxjs';

import { getBaseUrl } from '../../constants/api';
import {
  RESET_PASSWORD,
  SidebarViewState,
  SIGNUP,
  TOKEN_KEY,
} from '../../constants/appConstants';
import { Signup } from '../../models/signup.model';
import { AuthService } from '../../services/auth.service';
import { ValidateTokenParams } from 'src/app/models/reset-password.model';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss'],
})
export class ResetPasswordComponent implements OnInit {
  validToken = false;

  token!: string;

  firstPassword = new FormControl('');

  secondPassword = new FormControl('');

  passwordState = 'invalidLength';

  verifyPasswordState = 'verifyPassword';

  type: string | null = null;

  signUpUserData: Signup = {} as Signup;

  invalidPassword = false;

  protected readonly SETTINGS = SidebarViewState.SETTINGS;

  protected readonly RESET_PASSWORD = RESET_PASSWORD;

  constructor(
    private route: ActivatedRoute,
    private authService: AuthService,
    private router: Router,
    private http: HttpClient
  ) {
    this.route.queryParams.subscribe(params => {
      if (router.url.includes(SidebarViewState.SETTINGS)) {
        this.type = SidebarViewState.SETTINGS;
        this.validToken = true;
      } else if (router.url.includes(SIGNUP)) {
        this.type = SIGNUP;
        this.token = params[TOKEN_KEY];
        this.validateSignUpToken(params[TOKEN_KEY]);
      } else {
        this.type = RESET_PASSWORD;
        this.token = params[TOKEN_KEY];
        this.validateToken(this.token);
      }
    });
  }

  ngOnInit() {
    this.firstPassword.valueChanges
      .pipe(debounceTime(1000))
      .subscribe(value => {
        this.onFirstPasswordChange(value || '');
        this.onSecondPasswordChange(this.secondPassword.value || '');
      });
    this.secondPassword.valueChanges.subscribe(value => {
      this.onSecondPasswordChange(value || '');
    });
  }

  private onFirstPasswordChange(value: string) {
    this.invalidPassword = false;

    if (value.trim().length >= 12) {
      if (value.trim().length >= 99) {
        this.passwordState = 'maxLengthError';
      }
      this.validatePassword(value);
    } else {
      this.passwordState = 'invalidLength';
    }
  }

  private onSecondPasswordChange(value: string) {
    this.invalidPassword = false;

    if (value.trim().length >= 12) {
      if (this.firstPassword.value === value) {
        this.verifyPasswordState = 'passwordMatching';
      } else {
        this.verifyPasswordState = 'passwordNotMatching';
      }
    } else {
      this.verifyPasswordState = 'verifyPassword';
    }
  }

  private validateToken(token: string) {
    const params: ValidateTokenParams = {
      token,
      type: 'forgot-password',
    };
    this.authService.validateToken(params)?.subscribe({
      next: () => {
        this.validToken = true;
      },
      error: () => {
        this.validToken = false;
      },
    });
  }

  private validatePassword(value: string) {
    this.passwordState = 'checking';

    this.authService.validatePassword(value)?.subscribe({
      next: data => {
        if (data) {
          this.passwordState = 'success';
        } else {
          this.passwordState = 'tooObvious';
        }
      },
    });
  }

  onClickResetPassword() {
    const password = this.secondPassword.value || '';

    this.authService.setNewPassword(password);
    this.authService.setAuthenticationType(RESET_PASSWORD);
    this.authService.setTwoFactorVerificationType(RESET_PASSWORD);

    switch (this.type) {
      case RESET_PASSWORD:
        this.handleResetPasswordOnLogin();
        break;
      case SIGNUP:
        this.handlePasswordSetOnSignUp();
        break;
      default:
        this.handleResetPasswordInSettings();
    }
  }

  private handleResetPasswordOnLogin() {
    this.authService.setResetPasswordToken(this.token || '');
    this.authService.isLogin.next(true);

    const payload = {
      password: this.secondPassword.value || '',
      token: this.token,
      isReset: true,
    };

    this.invalidPassword = false;
    this.authService.isValidPassword(payload).subscribe({
      next: result => {
        if (result?.message) {
          this.invalidPassword = false;
          this.router.navigate(['/sessions/two-factor']);
        }
      },
      error: () => {
        this.invalidPassword = true;
      },
    });
  }

  private handlePasswordSetOnSignUp() {
    this.authService.setTwoFactorVerificationType(SIGNUP);
    const url = new URL(this.signUpUserData.mfa.otpURL);
    const params = new URLSearchParams(url.search);
    const secret = params.get('secret');
    this.authService.setSecretKey(secret || '');
    this.authService.setQRCodeMFA(this.signUpUserData.mfa.qrCodeData);
    this.authService.setSignUpPassword(this.secondPassword.value || '');
    this.authService.setSignUpUserData(this.signUpUserData);
    this.authService.setSignUpToken(this.token || '');
    this.authService.isLogin.next(true);
    this.router.navigate(['/sessions/two-factor']);
  }

  private handleResetPasswordInSettings() {
    const password = this.secondPassword.value || '';
    const userId = AuthService.getUser()?.sub!;
    const payload = {
      userId,
      password,
    };

    this.invalidPassword = false;
    this.authService.validatePasswordReset(payload).subscribe({
      next: result => {
        if (result?.message) {
          this.invalidPassword = false;
          this.router.navigate(['two-factor'], { relativeTo: this.route });
        }
      },
      error: () => {
        this.invalidPassword = true;
      },
    });
  }

  private validateSignUpToken(token: string) {
    this.getSignUpUserData(token);
  }

  private getSignUpUserData(token: string) {
    const params = { token };
    this.validToken = true;
    this.http
      .get<Signup>(`${getBaseUrl()}/emailtoken/register`, { params })
      .subscribe({
        next: data => {
          this.signUpUserData = data;
          this.validToken = true;
        },
        error: () => {
          this.validToken = false;
        },
      });
  }

  protected readonly SIGNUP = SIGNUP;
}
