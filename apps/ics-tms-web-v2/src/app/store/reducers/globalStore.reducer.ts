import { createReducer, on } from '@ngrx/store';
import {
  openSidebar,
  closeSidebar,
  setHeaderName,
  setLoginUserDetails,
  setApiUrl,
  setFeatureFlag,
  loginSuccess,
  isCompanyRuleCalled,
  setIsCaptchaEnabled,
  setCaptchaSiteKey,
  setFileDownloadsLimits,
} from '../actions/globalStore.actions';
import { GlobalState } from 'src/app/models/common';

export const initialState: GlobalState = {
  globalSidebarOpen: false,
  headerName: 'Dashboard',
  userDetails: {
    email: '',
    password: '',
    fullName: '',
    sub: '',
  },
  apiUrl: '',
  featureFlags: [],
  isSuccess: false,
  isCompanyRuleCalled: false,
  captcha: {
    enabled: false,
    siteKey: '',
  },
  fileDownloadsLimits: {
    siteLimit: -1,
    targetLimit: -1,
  },
};

export const GlobalStoreReducer = createReducer(
  initialState,
  on(openSidebar, state => ({
    ...state,
    globalSidebarOpen: true,
  })),

  on(closeSidebar, state => ({
    ...state,
    globalSidebarOpen: false,
  })),
  on(setHeaderName, (state, { name }) => ({
    ...state,
    headerName: name,
  })),
  on(setLoginUserDetails, (state, { userDetails }) => ({
    ...state,
    userDetails,
  })),
  on(setApiUrl, (state, { apiUrl }) => ({
    ...state,
    apiUrl,
  })),
  on(setFeatureFlag, (state, { featureFlags }) => ({
    ...state,
    featureFlags,
  })),
  on(loginSuccess, (state, { success }) => ({
    ...state,
    isSuccess: success,
  })),
  on(isCompanyRuleCalled, (state, { isCalled }) => ({
    ...state,
    isCompanyRuleCalled: isCalled,
  })),
  on(setIsCaptchaEnabled, (state, { isCaptchaEnabled }) => ({
    ...state,
    captcha: { ...state.captcha, enabled: isCaptchaEnabled },
  })),
  on(setCaptchaSiteKey, (state, { siteKey }) => ({
    ...state,
    captcha: { ...state.captcha, siteKey },
  })),
  on(setFileDownloadsLimits, (state, { siteLimit, targetLimit }) => ({
    ...state,
    fileDownloadsLimits: { siteLimit, targetLimit },
  }))
);
