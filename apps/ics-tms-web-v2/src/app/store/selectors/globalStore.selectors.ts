import { createSelector, createFeatureSelector } from '@ngrx/store';

import { GlobalState } from 'src/app/models/common';

export const globalState = createFeatureSelector<GlobalState>('globalState');

export const getLoginUserDetailsData = createSelector(
  globalState,
  state => state.userDetails
);

export const getApiUrl = createSelector(globalState, state => state.apiUrl);

export const getFeatureFlags = createSelector(
  globalState,
  state => state.featureFlags
);

export const isLoginSuccessfull = createSelector(
  globalState,
  state => state.isSuccess
);

export const getLoggedInUserCompanyId = createSelector(
  globalState,
  state => state.userDetails.company?.id
);

export const isCompanyRuleCalledSelect = createSelector(
  globalState,
  state => state.isCompanyRuleCalled
);

export const isCaptchaEnabled = createSelector(
  globalState,
  state => state.captcha.enabled
);

export const captchSiteKey = createSelector(
  globalState,
  state => state.captcha.siteKey
);

export const getFileDownloadsLimit = createSelector(
  globalState,
  state => state.fileDownloadsLimits
);

export const selectGlobalState = createSelector(globalState, state => state);

export const selectSideBarStatus = createSelector(
  selectGlobalState,
  state => state.globalSidebarOpen
);
