import { createAction, props } from '@ngrx/store';
import { Tags } from '../../models/tags.model';

const LOAD_TAGS = '[Tags] Load Tags';
const LOAD_TAGS_SUCCESS = '[Tags] Load Tags Success';
const LOAD_TAGS_FAILURE = '[Tags] Load Tags Failure';

export const loadTags = createAction(LOAD_TAGS);

export const loadTagsSuccess = createAction(
  LOAD_TAGS_SUCCESS,
  props<{ tagsData: Tags[] }>()
);

export const loadTagsFailure = createAction(
  LOAD_TAGS_FAILURE,
  props<{ error: string }>()
);
