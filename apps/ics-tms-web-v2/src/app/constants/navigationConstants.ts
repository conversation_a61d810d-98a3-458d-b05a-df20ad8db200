export const NAV_ITEMS = {
  DASHBOARD: {
    label: 'Dashboard',
    icon: 'gicon-dasboard',
  },
  ASSET_MANAGEMENT: {
    label: 'Asset Management',
  },
  SITES: {
    label: 'Sites',
    icon: 'ii-site menu-site-icon',
  },
  DEVICES: {
    label: 'Devices',
    icon: 'gicon-terminal',
  },
  REMOTE_MANAGEMENT: {
    label: 'Remote Management',
  },
  FILE_DOWNLOADS: {
    label: 'File Downloads',
    icon: 'gicon-calendar_schedule',
  },
  OFFLINE_PACKAGES: {
    label: 'Offline Packages',
    icon: 'fa fa-usb',
  },
  FILE_LIBRARY: {
    label: 'File Library',
    icon: 'gicon-flag',
  },
  BULK_OPERATIONS: {
    label: 'Bulk Operations',
    icon: 'fa fa-list',
  },
  CONFIG_MANAGEMENT: {
    label: 'Config Management',
  },
  CONFIGURATION: {
    label: 'Configuration',
    icon: 'fa fa-file-text',
  },
  DEPLOYMENT: {
    label: 'Deployment',
    icon: 'fa fas fa-rocket',
    customClass: 'deployment-menu-item',
  },
  FUEL_PRICE_MANAGEMENT: {
    label: 'Fuel Price Management',
  },
  FUEL_PRICES: {
    label: 'Fuel Prices',
    icon: 'fa fa-random',
  },
  MEDIA_MANAGEMENT: {
    label: 'Media Management',
  },
  LIBRARY: {
    label: 'Library',
    icon: 'gicon-photo',
  },
  PROMPT_SETS: {
    label: 'Prompt Sets',
    icon: 'gicon-media_playlists',
  },
  MEDIA_DOWNLOADS: {
    label: 'Media Downloads',
    icon: 'gicon-calendar_schedule',
  },
  PLAYLIST_MANAGEMENT: {
    label: 'Playlist Management',
  },
  CONTENT: {
    label: 'Content',
    icon: 'gicon-photo',
  },
  COUPONS: {
    label: 'Coupons',
    icon: 'gicon-terminal',
  },
  PLAYLISTS: {
    label: 'Playlists',
    icon: 'fa fa-play-circle',
  },
  REPORT_MANAGEMENT: {
    label: 'Report Management',
  },
  REPORTING: {
    label: 'Reporting',
    icon: 'gicon-reporting',
  },
  SCHEDULE_LIST: {
    label: 'Schedule List',
    icon: 'gicon-schedule',
  },
  REMOTE_KEY_INJECTION: {
    label: 'Remote Key Injection',
  },
  RKI: {
    label: 'RKI',
    icon: 'gicon-remote_key',
  },
  SETTINGS: {
    label: 'Settings',
    icon: 'gicon-settings',
  },
};

export enum Permissions {
  REMOTE_MGMT_VIEW = 'REMOTE_MGMT_VIEW',
  GET_ROLLOUT = 'GET_ROLLOUT',
  CONFIG_MGMT_VIEW = 'CONFIG_MGMT_VIEW',
  VIEW_CONFIG_MGMT_MENU = 'VIEW_CONFIG_MGMT_MENU',
  MEDIA_VIEW = 'MEDIA_VIEW',
  VIEW_FUEL_PRICE_MGMT_MENU = 'VIEW_FUEL_PRICE_MGMT_MENU',
  REPORTS_VIEW = 'REPORTS_VIEW',
  GET_RKI = 'GET_RKI',
  RKI_VIEW = 'RKI_VIEW',
  SETTINGS_VIEW = 'SETTINGS_VIEW',
  VIEW_COMPANY_SETTINGS = 'VIEW_COMPANY_SETTINGS',
  BULK_OPERATIONS_VIEW = 'BULK_OPERATIONS_VIEW',
  GET_BULK_OPERATIONS = 'GET_BULK_OPERATIONS',
  VIEW_MEDIA_SETTINGS = 'VIEW_MEDIA_SETTINGS',
  WRITE_MEDIA_SETTINGS = 'WRITE_MEDIA_SETTINGS',
  MEDIA = 'MEDIA',
  PLAYLIST = 'PLAYLIST',
}

export enum SettingsPathSegments {
  MEDIA = 'media',
  ALARMS = 'alarms',
}
