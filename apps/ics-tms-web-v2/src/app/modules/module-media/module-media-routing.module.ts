import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MediaLibraryComponent } from './components/media-library/media-library.component';
import { ModuleMediaComponent } from './module-media.component';
import { EmptyComponent } from 'src/app/components/empty-component/empty.component';

const routes: Routes = [
  {
    path: '',
    component: ModuleMediaComponent,
    children: [
      {
        path: 'library',
        component: MediaLibraryComponent,
        data: { navTitle: 'Media Library' },
      },
      {
        path: 'promptsets',
        component: EmptyComponent,
        data: { navTitle: 'Prompt Sets' },
      },
      {
        path: 'promptsets/:id',
        component: EmptyComponent,
        data: { css: 'view-is-full', navTitle: 'Prompt Sets' },
      },
      {
        path: 'downloads',
        component: EmptyComponent,
        data: { navTitle: 'Media Downloads' },
      },
      {
        path: '**',
        component: EmptyComponent,
        data: { navTitle: 'Media Downloads' },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ModuleMediaRoutingModule {}
