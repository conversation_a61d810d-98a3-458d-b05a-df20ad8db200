<div class="row add-site-container">
  <div class="col-lg-10 mx-auto">
    <div class="row">
      <div class="col-lg-10 mx-auto">
        <form class="form">
          <div class="form-body">
            <div class="form-title">
              <h3>Add a site</h3>
            </div>

            <!-- SITE NAME -->
            <div class="col-md-11 p-0">
              <div
                class="form-field-container mb"
                [ngClass]="{ 'mb-0': isValidatingSiteName || !isValidSiteName }"
              >
                <label class="form-label col-left" for="txt-site-name">
                  Name
                </label>
                <input
                  #siteNameInput
                  type="text"
                  id="txt-site-name"
                  name="siteName"
                  placeholder="Enter a unique name for this site"
                  autocomplete="off"
                  spellcheck="false"
                  class="form-control input-sm"
                  [(ngModel)]="siteName"
                  (input)="onSiteNameInput()"
                  (keydown.enter)="$event.preventDefault()"
                />
              </div>

              <div class="site-error-message" *ngIf="isValidatingSiteName">
                <p class="no-tag validating">Validating...</p>
              </div>

              <div
                class="site-error-message"
                *ngIf="!isValidatingSiteName && !isValidSiteName"
              >
                <p class="no-tag">{{ siteNameError }}</p>
              </div>
            </div>

            <!-- SITE ADDRESS -->
            <div class="col-md-11 p-0">
              <div
                class="form-field-container mb-15px"
                [ngClass]="{ 'mb-0': invalidAddress }"
              >
                <label class="form-label col-left" for="address">
                  Address
                </label>
                <input
                  #addressInput
                  id="address"
                  type="text"
                  placeholder="Search by business name or address"
                  spellcheck="false"
                  class="form-control input-sm"
                  (keydown.enter)="$event.preventDefault()"
                />
              </div>

              <div class="site-error-message" *ngIf="invalidAddress">
                <p class="txt-invalid-address">Please select a valid address</p>
              </div>
            </div>

            <!-- SITE GROUPS -->
            <div class="col-md-11 p-0">
              <div class="form-field-container mb-15px">
                <label for="siteGroup" class="form-label col-left">
                  Site Group
                </label>
                <ng-select
                  placeholder="Select site group"
                  bindValue="id"
                  bindLabel="name"
                  class="siteGroups"
                  [items]="siteGroups"
                  (search)="onSearch($event)"
                  (change)="onSiteGroupChange($event)"
                ></ng-select>
              </div>
            </div>

            <!-- SITE TAGS -->
            <div class="col-md-11 p-0">
              <div class="form-field-container tags-input">
                <label for="tags" class="form-label col-left"> Tags </label>
                <ng-select
                  [addTag]="addTag"
                  [items]="displayedTags"
                  [hideSelected]="true"
                  [multiple]="true"
                  bindLabel="name"
                  bindValue="id"
                  placeholder="Select or add new tags"
                  class="tags"
                  (change)="onTagsSelect($event)"
                  (search)="onTagSearch($event)"
                  (blur)="onTagSearch({ term: '' })"
                >
                  <ng-template ng-tag-tmp let-search="searchTerm">
                    {{ search }} (Add tag)
                  </ng-template>
                </ng-select>
              </div>
            </div>

            <div class="text-flex">
              <p class="no-tag" *ngIf="noTagMessage">
                {{ noTagMessage }}
              </p>
            </div>

            <!-- RKI -->
            <div class="col-md-11 p-0">
              <div
                class="form-field-container mb-15px"
                [ngClass]="minHeightClass"
                *ngIf="canSeeRKI()"
              >
                <label for="rki-key" class="form-label col-left">
                  RKI Key
                </label>
                <div ngbDropdown class="d-inline-block rki-dropdown">
                  <button
                    class="btn btn-outline-primary text-left"
                    id="selectRkiKey"
                    ngbDropdownToggle
                  >
                    {{ selectedRki }}
                  </button>
                  <div ngbDropdownMenu aria-labelledby="selectRkiKey">
                    <button
                      class="dropdown-item"
                      (click)="selectRki('--None--')"
                      [ngClass]="{ selected: isSelected('--None--') }"
                    >
                      <div class="title">--None--</div>
                    </button>
                    <button
                      class="dropdown-item"
                      *ngFor="let rki of rkiKeys"
                      (click)="selectRki(rki.id)"
                      [ngClass]="{ selected: isSelected(rki.name) }"
                    >
                      <div class="title">{{ rki.name }}</div>
                      <small class="description">{{ rki.owner.name }}</small>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- HIDDEN -->
            <div class="row mb-5px">
              <label class="col-md-2"></label>
              <div class="col-md-9">
                <button
                  type="button"
                  id="cb-hidden"
                  class="btn btn-check-box no-focus px-0"
                  [class]="{ active: visible }"
                  (click)="toggleVisible()"
                >
                  <span
                    aria-hidden="true"
                    class="icon"
                    [class]="{
                      'gicon-check_box_outline_blank': !visible,
                      'gicon-check_box': visible,
                    }"
                  ></span>
                </button>
                <label for="cb-hidden" class="control-label control-label-sm">
                  Hidden
                  <i
                    class="fa fa-info-circle site-info-tooltip-icon"
                    tooltipClass="site-info-tooltip"
                    placement="bottom-left"
                    [ngbTooltip]="visibleTooltip"
                  ></i>
                </label>
              </div>
            </div>

            <!-- SUPPRESS ALARM -->
            <div class="row mb-15px">
              <label class="col-md-2"></label>

              <div class="col-md-9">
                <button
                  type="button"
                  id="chkIsHidden"
                  class="btn btn-check-box no-focus px-0"
                  [class]="{ active: isSupressAlarmClicked }"
                  (click)="toggleSuppressOffhoursAlarm()"
                  [disabled]="!suppressOffhoursAlarm"
                >
                  <span
                    aria-hidden="true"
                    class="icon"
                    [class]="{
                      'gicon-check_box_outline_blank': !isSupressAlarmClicked,
                      'gicon-check_box': isSupressAlarmClicked,
                      disabled: !suppressOffhoursAlarm,
                    }"
                  ></span>
                </button>
                <label
                  for="chkIsHidden"
                  class="control-label control-label-sm"
                  [class]="{ disabled: !suppressOffhoursAlarm }"
                >
                  FC powered off during non-operating hours
                  <i
                    class="fa fa-info-circle site-info-tooltip-icon"
                    tooltipClass="site-info-tooltip"
                    placement="bottom-left"
                    [ngbTooltip]="hoursTooltip"
                  ></i>
                </label>
              </div>
            </div>

            <!-- DISABLE SOFTWARE DOWNLOADS -->
            <div class="row mb-15px">
              <label class="col-md-2"></label>

              <div class="col-md-9">
                <button
                  type="button"
                  id="chkIsHidden"
                  class="btn btn-check-box no-focus px-0"
                  [class]="{ active: disableFileDownloads }"
                  (click)="toggleDisableFileDownloads()"
                >
                  <span
                    aria-hidden="true"
                    class="icon"
                    [class]="{
                      'gicon-check_box_outline_blank': !disableFileDownloads,
                      'gicon-check_box': disableFileDownloads,
                    }"
                  ></span>
                </button>
                <label for="chkIsHidden" class="control-label control-label-sm">
                  Disable software downloads
                  <i
                    class="fa fa-info-circle site-info-tooltip-icon"
                    tooltipClass="site-info-tooltip"
                    placement="bottom-left"
                    ngbTooltip="This will disable file downloads."
                  ></i>
                </label>
              </div>
            </div>

            <!-- DISABLE CONFIG MANAGEMENT -->
            <div class="row mb-15px">
              <label class="col-md-2"></label>

              <div class="col-md-9">
                <button
                  type="button"
                  id="cb-disable-cm-automatic-deployments"
                  class="btn btn-check-box no-focus px-0"
                  [class]="{ active: disableCMAutomaticDeployments }"
                  (click)="toggleDisableCMAutomaticDeployments()"
                >
                  <span
                    aria-hidden="true"
                    class="icon"
                    [class]="{
                      'gicon-check_box_outline_blank':
                        !disableCMAutomaticDeployments,
                      'gicon-check_box': disableCMAutomaticDeployments,
                    }"
                  ></span>
                </button>
                <label
                  for="cb-disable-cm-automatic-deployments"
                  class="control-label control-label-sm"
                >
                  Disable config management automatic deployments
                  <i
                    class="fa fa-info-circle site-info-tooltip-icon"
                    tooltipClass="site-info-tooltip"
                    placement="bottom-left"
                    ngbTooltip="This will disable config management automatic deployments."
                  ></i>
                </label>
              </div>
            </div>

            <div class="col-md-11 p-0">
              <app-store-hours
                (getStorehours)="handleStoreHours($event)"
              ></app-store-hours>
            </div>

            <hr />

            <!-- Optional  -->
            <div>
              <h5 class="text-grey">OPTIONAL</h5>

              <div class="col-md-11 p-0">
                <div class="form-field-container mb">
                  <label for="ref-id1" class="form-label col-left">
                    Reference ID
                  </label>
                  <input
                    type="text"
                    class="form-control input-sm"
                    id="ref-id1"
                    [(ngModel)]="refrenceID"
                    name="refrenceID"
                    autocomplete="off"
                    spellcheck="false"
                    (keydown.enter)="$event.preventDefault()"
                  />
                </div>
              </div>

              <div class="col-md-11 p-0">
                <div class="form-field-container mb">
                  <label for="ref-id2" class="form-label col-left">
                    Phone
                  </label>
                  <input
                    type="text"
                    class="form-control input-sm"
                    id="ref-id2"
                    [(ngModel)]="phone"
                    name="phone"
                    autocomplete="off"
                    spellcheck="false"
                    (keydown.enter)="$event.preventDefault()"
                  />
                </div>
              </div>

              <div class="col-md-11 p-0">
                <div class="form-field-container">
                  <label for="ref-id3" class="form-label col-left">
                    Email
                  </label>
                  <input
                    type="text"
                    class="form-control input-sm"
                    id="ref-id3"
                    [(ngModel)]="email"
                    [formControl]="emailFormControl"
                    name="email"
                    autocomplete="off"
                    spellcheck="false"
                    (keydown.enter)="$event.preventDefault()"
                  />
                </div>

                <div
                  *ngIf="
                    emailFormControl.hasError('pattern') &&
                    !emailFormControl.hasError('required')
                  "
                  class="site-error-message n-mb"
                >
                  <p class="no-tag">Invalid email address</p>
                </div>
              </div>
            </div>

            <hr />

            <!-- OPTIONAL EXTERNAL REFERENCE -->
            <div class="external-references">
              <h5 class="text-grey">OPTIONAL EXTERNAL REFERENCES</h5>

              <app-external-reference
                (getExternalType)="handleExternalType($event)"
              ></app-external-reference>
            </div>
          </div>

          <div class="form-footer">
            <button
              class="btn btn-primary btn-box-shadow btn-wide"
              [disabled]="isAddingSite || isAddSiteFormDisable()"
              [ngClass]="{ 'disable-button': isAddSiteFormDisable() }"
              (click)="selectedRKIKeyID === null ? onAddSite() : openMfaModal()"
            >
              {{ isAddingSite ? 'Adding' : 'Add Site' }}
              <i *ngIf="isAddingSite" class="fa fa-spinner fa-pulse fa-fw"></i>
            </button>

            <button class="btn btn-default" (click)="goBack()">Back</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
