import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { AddSiteFormComponent } from './components/add-site-form/add-site-form.component';
import { authGuard } from 'src/app/auth.guard';
import { EmptyComponent } from 'src/app/components/empty-component/empty.component';

const routes: Routes = [
  {
    path: 'add',
    component: AddSiteFormComponent,
    canActivate: [authGuard],
  },
  {
    path: '**',
    component: EmptyComponent,
    data: { css: 'view-is-full', navTitle: 'Asset Management' },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ModuleSiteRoutingModule {}
