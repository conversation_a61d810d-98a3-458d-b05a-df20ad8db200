import { Location } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Actions, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { isEmpty } from 'lodash';
import {
  BehaviorSubject,
  combineLatest,
  filter,
  firstValueFrom,
  interval,
  map,
  Subject,
  takeUntil,
} from 'rxjs';
import {
  AUTO_POLL_INTERVAL,
  COLOR_LEVELS,
  DEFAULT_PAGE_SIZE,
  FileDownloadConstants,
  LINK_TAG,
  Messages,
  NOTIFICATION_LEVEL_SUCCESS,
  NotificationDateFormats,
  NotificationEntityTypes,
  PackageNotificationTypes,
  NotificationRoutes,
  PromptSetNotificationTypes,
  ICON_CLASS_MAP,
} from '../../constants/appConstants';
import {
  Notification,
  NotificationData,
  NotificationParams,
  PullFilePackage,
} from '../../models/notification.model';
import { NotificationsService } from '../../services/notifications.service';
import * as notificationActions from '../../store/actions/notifications.actions';
import * as notificationSelectors from '../../store/selectors/notifications.selectors';
import { CommonResultMetadata, PaginationState } from 'src/app/models/common';
import { OfflinePackage } from 'src/app/modules/modules-remote/models/OfflinePackage.model';
import { AuthService } from 'src/app/services/auth.service';
import { ToastService } from 'src/app/services/toast.service';
import {
  loadUnreadCount,
  loadUnreadCountSuccess,
} from 'src/app/store/actions/unread-notifications.actions';
import { selectUnreadCountData } from 'src/app/store/selectors/unread-notifications.selector';

dayjs.extend(utc);
@Component({
  selector: 'app-notification-page',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.scss'],
})
export class NotificationPageComponent implements OnInit {
  store = inject(Store);

  router = inject(Router);

  authService = inject(AuthService);

  location = inject(Location);

  notificationService = inject(NotificationsService);

  toastService = inject(ToastService);

  actions$ = inject(Actions);

  notifications$ = new BehaviorSubject<NotificationData>(
    {} as NotificationData
  );

  isLoading$ = this.store.select(
    notificationSelectors.selectNotificationLoading
  );

  unreadCount = 0;

  companyName = '';

  currentParams: NotificationParams = {
    autoPoll: true,
    pageIndex: 0,
    pageSize: DEFAULT_PAGE_SIZE,
  };

  paginationState$ = new BehaviorSubject<PaginationState>({
    currentPage: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    totalResults: 0,
    isPaginationVisible: false,
  });

  private readonly destroy$ = new Subject<void>();

  vm$ = combineLatest([
    this.notifications$,
    this.paginationState$,
    this.isLoading$,
  ]).pipe(
    map(([notifications, paginationState, isLoading]) => ({
      notifications,
      paginationState,
      isLoading,
      hasNotifications: this.checkHasNotifications(notifications),
    }))
  );

  ngOnInit(): void {
    this.loadInitialData();
    this.subscribeToStoreUpdates();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadInitialData(): void {
    this.fetchUnreadCount();
    this.fetchNotifications();

    interval(AUTO_POLL_INTERVAL)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.fetchNotifications();
      });

    this.companyName = AuthService.getCompany()?.name.toLowerCase() || '';
  }

  private subscribeToStoreUpdates(): void {
    this.store
      .select(notificationSelectors.selectNotifications)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: notifications => {
          this.notifications$.next(notifications);
          this.updatePaginationState(notifications.resultsMetadata);
        },
      });

    this.store
      .select(selectUnreadCountData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(count => {
        this.unreadCount = count;
      });
  }

  private updatePaginationState(metadata: CommonResultMetadata): void {
    this.paginationState$.next({
      currentPage: metadata.pageIndex + 1,
      pageSize: DEFAULT_PAGE_SIZE,
      totalResults: metadata.totalResults,
      isPaginationVisible: metadata.totalResults > DEFAULT_PAGE_SIZE,
    });
  }

  async markAsRead(notification: Notification, event?: Event): Promise<void> {
    event?.stopPropagation();
    if (!notification.read) {
      this.store.dispatch(
        notificationActions.markNotificationAsRead({
          notificationId: notification.id,
          payload: { read: true },
        })
      );
      await firstValueFrom(
        this.actions$.pipe(
          ofType(notificationActions.markNotificationAsReadSuccess),
          filter(action => action.notificationId === notification.id)
        )
      );

      await this.fetchUnreadCount();
    }
  }

  async markAllAsRead(): Promise<void> {
    await firstValueFrom(this.notificationService.markAllAsRead());
    this.fetchNotifications();
    await this.fetchUnreadCount();
  }

  private fetchNotifications(): void {
    this.store.dispatch(
      notificationActions.loadNotifications({ params: this.currentParams })
    );
  }

  private async fetchUnreadCount(): Promise<void> {
    this.store.dispatch(
      loadUnreadCount({
        params: { autoPoll: this.currentParams.autoPoll },
      })
    );

    await firstValueFrom(this.actions$.pipe(ofType(loadUnreadCountSuccess)));
  }

  getLevelColor(level: string): string {
    return COLOR_LEVELS[level] || '';
  }

  private checkHasNotifications(notifications: NotificationData): boolean {
    return notifications?.categorizedNotifications?.some(n => !isEmpty(n.data));
  }

  async openNotification(notification: Notification): Promise<void> {
    if (!notification.read) {
      await this.markAsRead(notification);
    }

    const { type, id, path } = notification.relatedEntity ?? {};

    switch (type || notification.type) {
      case NotificationEntityTypes.FILE_DOWNLOAD_REQUEST:
        this.router.navigate([NotificationRoutes.FILE_DOWNLOAD_REQUEST(id)]);
        break;
      case NotificationEntityTypes.DEVICE:
        this.handleDeviceNotification(id);
        break;
      case NotificationEntityTypes.SITE:
        this.router.navigate([NotificationRoutes.SITE_DETAILS(id)]);
        break;
      case NotificationEntityTypes.USER:
        this.router.navigate([
          NotificationRoutes.USER_SETTINGS(this.companyName, id),
        ]);
        break;
      case NotificationEntityTypes.RKI_REQUEST:
        this.handleRkiNotification(id);
        break;
      case NotificationEntityTypes.PROMPT_SET:
        this.handlePromptSetNotification(id, notification.type);
        break;
      case NotificationEntityTypes.SOFTWARE_PACKAGE:
        this.router.navigate([NotificationRoutes.SOFTWARE_PACKAGES], {
          queryParams: { expanded: id },
        });
        break;
      case NotificationEntityTypes.ASSET_PACKAGE:
        this.router.navigate([NotificationRoutes.MEDIA_LIBRARY]);
        break;
      case NotificationEntityTypes.FPS:
        this.handleFuelPriceNotification(path);
        break;
      case NotificationEntityTypes.SITE_CUSTOM_ATTRIBUTE:
        this.router.navigate([NotificationRoutes.CUSTOM_ATTRIBUTE(id)]);
        break;
      case NotificationEntityTypes.CONFIG_INSTANCE:
        this.router.navigate([NotificationRoutes.CONFIG_INSTANCE(id)]);
        break;
      default:
        break;
    }
  }

  private handleFuelPriceNotification(path?: string): void {
    if (path) {
      this.location.go(path);
      return;
    }
    this.router.navigate([NotificationRoutes.FUEL_EVENT]);
  }

  private handleDeviceNotification(deviceId: string): void {
    this.notificationService.getDevice(deviceId).subscribe({
      next: (res: any) => {
        const siteId = res?.data?.siteId;
        if (siteId) {
          this.router.navigate([
            NotificationRoutes.DEVICE_OVERVIEW(siteId, deviceId),
          ]);
        }
      },
    });
  }

  private handlePromptSetNotification(id: string, type: string): void {
    if (type === PromptSetNotificationTypes.APPROVAL) {
      this.router.navigate([NotificationRoutes.PROMPT_SET_DETAILS(id)], {
        queryParams: { readOnly: true },
      });
    } else if (type === PromptSetNotificationTypes.IMPORT_READY) {
      this.router.navigate([NotificationRoutes.PROMPT_SET_LIST]);
    }
  }

  private handleRkiNotification(id: string): void {
    this.notificationService.getRkiDetail(id).subscribe({
      next: res => {
        if (
          typeof res === 'string' &&
          res.includes(Messages.NOT_VALID_RKI_REQUEST)
        )
          return;
        this.router.navigate([NotificationRoutes.RKI_DETAILS(id)]);
      },
    });
  }

  handlePageChange(pageNumber: number): void {
    this.currentParams = { ...this.currentParams, pageIndex: pageNumber - 1 };
    this.fetchNotifications();
  }

  isDownloadable(notification: Notification): boolean {
    return (
      this.isFileUploadRequest(notification) ||
      this.isSoftwareOrRkiPackage(notification) ||
      this.isPromptSet(notification)
    );
  }

  async downloadNotificationData(notification: Notification): Promise<void> {
    if (!notification.read) await this.markAsRead(notification);

    if (this.isSoftwareOrRkiPackage(notification)) {
      this.downloadOfflinePackage(notification);
      return;
    }
    if (this.isPromptSet(notification)) {
      this.downloadPromptSet(notification);
      return;
    }
    this.initiateDownload(notification);
  }

  private isFileUploadRequest(notification: Notification): boolean {
    return (
      ![
        NotificationEntityTypes.SOFTWARE_PACKAGE,
        NotificationEntityTypes.RKI_PACKAGE,
      ].includes(notification.relatedEntity.type as NotificationEntityTypes) &&
      notification.level === NOTIFICATION_LEVEL_SUCCESS &&
      [
        PackageNotificationTypes.DOWNLOAD,
        PackageNotificationTypes.OFFLINE,
      ].includes((notification.kind ?? '') as PackageNotificationTypes)
    );
  }

  private isSoftwareOrRkiPackage(notification: Notification): boolean {
    return (
      [
        NotificationEntityTypes.SOFTWARE_PACKAGE,
        NotificationEntityTypes.RKI_PACKAGE,
      ].includes(notification.relatedEntity.type as NotificationEntityTypes) &&
      notification.level === NOTIFICATION_LEVEL_SUCCESS
    );
  }

  private isPromptSet(notification: Notification): boolean {
    return (
      notification.relatedEntity.type === NotificationEntityTypes.PROMPT_SET &&
      Boolean(notification.relatedEntity.href) &&
      notification.level === NOTIFICATION_LEVEL_SUCCESS
    );
  }

  private initiateDownload(notification: Notification): void {
    const { id, type } = notification.relatedEntity ?? {};
    if (type === NotificationEntityTypes.FILE_UPLOAD_REQUEST) {
      this.notificationService.getPackageUrl(id).subscribe({
        next: (response: PullFilePackage) => {
          const { name } = response ?? {};
          this.notificationService.downloadPackage(id, name);
        },
      });
    }
  }

  private downloadOfflinePackage(notification: Notification): void {
    this.notificationService.getOfflinePackage(notification).subscribe({
      next: (offlinePackage: OfflinePackage) => {
        this.notificationService
          .downloadOfflinePackage(offlinePackage)
          .subscribe({
            next: response => {
              const blob = new Blob([response], {
                type: FileDownloadConstants.ZIP_MIME_TYPE,
              });
              const url = window.URL.createObjectURL(blob);
              const link = document.createElement(LINK_TAG);
              link.href = url;
              link.download = `${offlinePackage.name}.${FileDownloadConstants.ZIP_EXTENSION}`;
              link.click();
            },
          });
      },
      error: err => {
        if (err.status === 404) {
          this.toastService.show({
            message: Messages.OFFLINE_PACKAGE_NOT_FOUND,
          });
          this.store.dispatch(
            notificationActions.disableNotificationDownload({
              notificationId: notification.id,
            })
          );
        }
      },
    });
  }

  private async downloadPromptSet(notification: Notification): Promise<void> {
    const { id, href } = notification.relatedEntity ?? {};
    if (href) {
      const link = document.createElement(LINK_TAG);
      link.href = href;
      link.download = id;
      link.click();
    }
    if (!notification.read) await this.markAsRead(notification);
  }

  getNotificationDate(time: string): string {
    return dayjs(time).format(NotificationDateFormats.FULL);
  }

  getNotificationTimeLabel(date: string | Date): string {
    const d = dayjs(date);
    if (d.isToday()) {
      return d.fromNow();
    }
    if (d.isYesterday()) {
      return d.format(NotificationDateFormats.TIME_ONLY);
    }
    return d.format(NotificationDateFormats.SHORT_WITH_DAY);
  }

  getIconClass(kind: string): string | null {
    return ICON_CLASS_MAP[kind] ?? null;
  }
}
