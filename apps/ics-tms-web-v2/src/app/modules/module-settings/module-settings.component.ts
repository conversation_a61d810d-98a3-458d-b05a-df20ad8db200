import { Component, inject, OnInit, ViewEncapsulation } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs';
import { COMPANY_NAME } from './constants/appConstants';
import { getCompanySettingsMap } from './utils/transform-data';
import { ListItem } from 'src/app/constants/settingsSideItems';
import { AuthService } from 'src/app/services/auth.service';

@Component({
  selector: 'app-module-settings',
  templateUrl: './module-settings.component.html',
  styleUrls: ['./module-settings.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ModuleSettingsComponent implements OnInit {
  selectedItem!: string;

  items!: ListItem[];

  router: Router = inject(Router);

  ngOnInit(): void {
    this.items = getCompanySettingsMap();
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.updateSelectedItem();
      });
    this.updateSelectedItem();
  }

  updateSelectedItem() {
    this.selectedItem = this.getLastSegment(this.router.url)!;
  }

  selectItem(item: ListItem) {
    this.selectedItem = item.name;
  }

  private getLastSegment(url: string) {
    return getCompanySettingsMap().find(item => url.includes(item.urlEndPoint))
      ?.name;
  }

  getRouterLink(navigateLink: string): string {
    return navigateLink.replace(
      COMPANY_NAME,
      AuthService.getCompany()?.name.toLowerCase() || ''
    );
  }
}
