import { Component, inject, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs';
import {
  ACCOUNT_SETTINGS_MAP,
  ListItem,
} from 'src/app/constants/settingsSideItems';

@Component({
  selector: 'app-module-account-settings',
  templateUrl: './module-account-settings.component.html',
  styleUrls: ['./module-account-settings.component.scss'],
})
export class ModuleAccountSettingsComponent implements OnInit {
  selectedItem!: string;

  items = ACCOUNT_SETTINGS_MAP;

  router: Router = inject(Router);

  ngOnInit(): void {
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.updateSelectedItem();
      });
    this.updateSelectedItem();
  }

  private updateSelectedItem() {
    this.selectedItem = this.getLastSegment(this.router.url)!;
  }

  private getLastSegment(url: string) {
    return ACCOUNT_SETTINGS_MAP.find(item => url.includes(item.urlEndPoint))
      ?.name;
  }

  selectItem(item: ListItem) {
    this.selectedItem = item.name;
  }
}
