import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ModuleAccountSettingsComponent } from './module-account-settings.component';
import { AccountProfileComponent } from './components/account-profile/account-profile.component';
import { ResetPasswordComponent } from 'src/app/components/reset-password/reset-password.component';
import { TwoFactorComponent } from 'src/app/components/two-factor/two-factor.component';

const routes: Routes = [
  {
    path: '',
    component: ModuleAccountSettingsComponent,
    data: { navTitle: 'Account Settings' },
    children: [
      { path: '', redirectTo: 'account', pathMatch: 'full' },
      { path: 'account', component: AccountProfileComponent },
      { path: 'reset-password', component: ResetPasswordComponent },
      { path: 'reset-password/two-factor', component: TwoFactorComponent },
    ],
  },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ModuleAccountSettingsRoutingModule {}
