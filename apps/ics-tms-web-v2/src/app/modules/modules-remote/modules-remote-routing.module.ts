import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { ModulesRemoteComponent } from './modules-remote.component';

import { OfflinePackagesComponent } from './components/offline-packages/offline-packages.component';
import { CreatePackageComponent } from './components/offline-packages/create-package/create-package.component';
import { FileDownloadComponent } from './components/file-download/file-download.component';
import { FileLibraryComponent } from './components/file-library/file-library.component';
import { BulkOperationsListComponent } from './components/bulk-operations/bulk-operations-list/bulk-operations-list.component';
import { FILE_DOWNLOADS } from 'src/app/constants/globalConstant';
import { IcsStepperTemplateComponent } from 'src/app/components/shared/ics-stepper-template/ics-stepper-template.component';
import { TwoFactorComponent } from 'src/app/components/two-factor/two-factor.component';
import { EmptyComponent } from 'src/app/components/empty-component/empty.component';

const routes: Routes = [
  {
    path: '',
    component: ModulesRemoteComponent,
    children: [
      { path: 'downloads', component: FileDownloadComponent },
      {
        path: 'downloads/create',
        component: IcsStepperTemplateComponent,
        data: {
          type: FILE_DOWNLOADS,
          isCopy: false,
        },
      },
      {
        path: 'downloads/:id/copy',
        component: IcsStepperTemplateComponent,
        data: {
          type: FILE_DOWNLOADS,
          isCopy: true,
        },
      },
      {
        path: 'downloads/:id',
        component: FileDownloadComponent,
      },
      {
        path: 'packages',
        component: OfflinePackagesComponent,
      },
      { path: 'packages/create', component: CreatePackageComponent },
      { path: 'library', component: FileLibraryComponent },
      {
        path: 'bulk-operations',
        component: BulkOperationsListComponent,
        data: {
          navTitle: 'Bulk Operations',
        },
      },
      {
        path: 'bulk-operations/two-factor',
        component: TwoFactorComponent,
        data: {
          navTitle: 'Bulk Operations',
        },
      },
      {
        path: 'config-management',
        component: EmptyComponent,
        data: {
          navTitle: 'Config Management',
          css: 'view-is-full',
        },
        children: [
          {
            path: '**',
            component: EmptyComponent,
            data: {
              navTitle: 'Config Management',
              css: 'view-is-full',
            },
          },
        ],
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ModulesRemoteRoutingModule {}
