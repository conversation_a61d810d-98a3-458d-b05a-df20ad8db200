import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';

import { authGuard, noAuthGuard } from './auth.guard';
import { EmptyComponent } from './components/empty-component/empty.component';
import { ForgetPasswordComponent } from './components/forget-password/forget-password.component';
import { ResetEmailSentComponent } from './components/forget-password/reset-email-sent/reset-email-sent.component';
import { LicensesComponent } from './components/licenses/licenses.component';
import { LoginComponent } from './components/login/login.component';
import { PrivacyPolicyComponent } from './components/privacy-policy/privacy-policy.component';
import { ReleaseNotesComponent } from './components/release-notes/release-notes.component';
import { ResetPasswordComponent } from './components/reset-password/reset-password.component';
import { TermsAndConditionsComponent } from './components/terms-and-conditions/terms-and-conditions.component';
import { TwoFactorComponent } from './components/two-factor/two-factor.component';

const routes: Routes = [
  // #region Login
  {
    path: '',
    component: LoginComponent,
    canActivate: [noAuthGuard],
    data: { css: 'is-view-full' },
  },
  {
    path: 'legal/licenses',
    component: LicensesComponent,
    canActivate: [authGuard],
    data: { navTitle: 'Licenses' },
  },
  {
    path: 'release-notes',
    component: ReleaseNotesComponent,
    canActivate: [authGuard],
    data: { navTitle: 'Release Notes' },
  },
  {
    path: 'sessions/two-factor',
    component: TwoFactorComponent,
    canActivate: [noAuthGuard],
    data: { css: 'is-view-full' },
  },
  {
    path: 'signup',
    component: ResetPasswordComponent,
    data: { css: 'is-view-full' },
  },
  {
    path: 'resetpassword',
    component: ResetPasswordComponent,
    data: { css: 'is-view-full' },
  },
  {
    path: 'resend-password',
    component: ForgetPasswordComponent,
    data: { css: 'is-view-full' },
  },
  {
    path: 'resend-password/reset-email-sent',
    component: ResetEmailSentComponent,
    data: { css: 'is-view-full' },
  },
  {
    path: 'legal/privacy',
    component: PrivacyPolicyComponent,
    data: { css: 'is-view-full' },
  },
  {
    path: 'legal/terms',
    component: TermsAndConditionsComponent,
    data: { css: 'is-view-full' },
  },
  // #endregion

  // #region Sites
  {
    path: 'sites',
    loadChildren: () =>
      import('./modules/module-site/module-site.module').then(
        m => m.ModuleSiteModule
      ),
    canActivate: [authGuard],
    data: { navTitle: 'Asset Management' },
  },
  // #endregion

  // #region Empty components
  {
    path: 'dashboard',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { css: 'view-is-full', navTitle: 'Dashboard' },
  },
  {
    path: 'sites/:id',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { css: 'view-is-full', navTitle: 'Asset Management' },
  },
  {
    path: 'sites/:id/:deviceId/overview',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { css: 'view-is-full', navTitle: 'Device Overview' },
  },
  {
    path: 'secure-reports',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { css: 'view-is-full', navTitle: 'Secure Reports' },
  },
  {
    path: 'asset-management',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { css: 'view-is-full', navTitle: 'Asset Management' },
    children: [
      {
        path: 'sites/attributes/bulk-edit',
        component: EmptyComponent,
        data: { css: 'view-is-centered', navTitle: 'Asset Management' },
      },
      {
        path: 'sites/attributes/import-attributes',
        component: EmptyComponent,
        data: { css: 'view-is-centered', navTitle: 'Asset Management' },
      },
      {
        path: 'sites/attributes/import-devices',
        component: EmptyComponent,
        data: { css: 'view-is-centered', navTitle: 'Asset Management' },
      },
      {
        path: 'sites/attributes/import-devices/past-imports',
        component: EmptyComponent,
        data: {
          css: 'view-is-centered',
          navTitle: 'Asset Management',
        },
      },
      {
        path: 'sites/attributes/import-site-tags',
        component: EmptyComponent,
        data: { css: 'view-is-centered', navTitle: 'Asset Management' },
      },
      {
        path: 'sites/attributes/import-site-tags/past-imports',
        component: EmptyComponent,
        data: {
          css: 'view-is-centered',
          navTitle: 'Asset Management',
        },
      },
      {
        path: 'sites/attributes/delete-site-tags',
        component: EmptyComponent,
        data: { css: 'view-is-centered', navTitle: 'Asset Management' },
      },
      {
        path: 'sites/attributes/delete-site-tags/past-imports',
        component: EmptyComponent,
        data: {
          css: 'view-is-centered',
          navTitle: 'Asset Management',
        },
      },
      {
        path: '**',
        component: EmptyComponent,
        data: {
          css: 'view-is-full',
          navTitle: 'Asset Management',
        },
      },
    ],
  },
  {
    path: 'fuel-price-management',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: {
      css: 'view-is-full',
      navTitle: 'Fuel Price Management',
    },
    children: [
      {
        path: '**',
        component: EmptyComponent,
        data: {
          css: 'view-is-full',
          navTitle: 'Fuel Price Management',
        },
      },
    ],
  },
  {
    path: 'devices',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { navTitle: 'Asset Management' },
    children: [
      {
        path: '**',
        component: EmptyComponent,
        data: {
          navTitle: 'Asset Management',
        },
      },
    ],
  },
  // #endregion

  // #region Remote
  {
    path: 'remote',
    loadChildren: () =>
      import('./modules/modules-remote/modules-remote.module').then(
        m => m.ModulesRemoteModule
      ),
    canActivate: [authGuard],
    data: { navTitle: 'Remote Management' },
  },
  // #endregion

  // #region Settings
  {
    path: 'settings',
    loadChildren: () =>
      import(
        './modules/module-account-settings/module-account-settings.module'
      ).then(m => m.ModuleAccountSettingsModule),
    canActivate: [authGuard],
    data: { navTitle: 'Account Settings' },
  },

  // #region Settings
  {
    path: ':company-name/settings',
    loadChildren: () =>
      import('./modules/module-settings/module-settings.module').then(
        m => m.ModuleSettingsModule
      ),
    canActivate: [authGuard],
    data: { navTitle: 'Company Settings', css: 'view-is-full' },
  },
  // #endregion

  // #region Notifications
  {
    path: 'notifications',
    loadChildren: () =>
      import('./modules/module-notifications/module-notifications.module').then(
        m => m.ModuleNotificationsModule
      ),
    canActivate: [authGuard],
    data: { css: 'view-is-centered', navTitle: 'Notifications' },
  },
  // #endregion

  // #region RKI
  {
    path: 'rki',
    loadChildren: () =>
      import('./modules/module-rki/module-rki.module').then(
        m => m.ModuleRkiModule
      ),
    canActivate: [authGuard],
    data: { navTitle: 'RKI' },
  },
  // #endregion

  // #region Media
  {
    path: 'media',
    loadChildren: () =>
      import('./modules/module-media/module-media.module').then(
        m => m.ModuleMediaModule
      ),
    canActivate: [authGuard],
    data: { navTitle: 'Media Management' },
  },
  // #endregion

  // #region Playlist
  {
    path: 'playlist',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { css: 'view-is-centered', navTitle: 'Playlists' },
    children: [
      {
        path: 'content',
        component: EmptyComponent,
        data: { css: 'view-is-centered', navTitle: 'Playlists' },
      },
      {
        path: 'builder',
        component: EmptyComponent,
        data: { css: 'view-is-full', navTitle: 'Playlists' },
      },
      {
        path: 'builder/:id',
        component: EmptyComponent,
        data: { css: 'view-is-full', navTitle: 'Playlists' },
      },
      {
        path: 'coupon',
        component: EmptyComponent,
        data: { css: 'view-is-centered', navTitle: 'Playlists' },
      },
      {
        path: 'coupon/multi/:type',
        component: EmptyComponent,
        data: { css: 'view-is-centered', navTitle: 'Playlists' },
      },
      {
        path: '**',
        component: EmptyComponent,
        data: { css: 'view-is-centered', navTitle: 'Playlists' },
      },
    ],
  },
  // #endregion

  // #region Reporting
  {
    path: 'report-management',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { css: 'view-is-full', navTitle: 'Reports' },
    children: [
      {
        path: '**',
        component: EmptyComponent,
        data: { css: 'view-is-full', navTitle: 'Reports' },
      },
    ],
  },
  // #endregion

  // #region Schedule List
  {
    path: 'schedule-list',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { css: 'view-is-full', navTitle: 'Schedule' },
    children: [
      {
        path: '**',
        component: EmptyComponent,
        data: { css: 'view-is-full', navTitle: 'Schedule' },
      },
    ],
  },
  // #endregion

  {
    path: '**',
    component: EmptyComponent,
  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      preloadingStrategy: PreloadAllModules,
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
