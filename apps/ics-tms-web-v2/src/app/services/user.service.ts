import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { tap } from 'rxjs';
import { getApiConstants } from '../constants/api';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  httpClient = inject(HttpClient);

  getUserGuide() {
    const url = `${getApiConstants().docs.userGuide}`;
    return this.httpClient.get(url, { responseType: 'arraybuffer' }).pipe(
      tap(data => {
        const blob = new Blob([data], { type: 'application/pdf' });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = 'ICS_UserGuide.pdf';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      })
    );
  }
}
