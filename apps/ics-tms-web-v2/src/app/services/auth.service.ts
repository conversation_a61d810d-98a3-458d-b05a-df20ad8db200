import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { jwtDecode } from 'jwt-decode';
import { CookieService } from 'ngx-cookie-service';
import { BehaviorSubject, Observable } from 'rxjs';
import { getApiConstants, getBaseUrl } from '../constants/api';
import {
  APP_FEATURE_FLAGS,
  CSRF,
  CSRF_HASH,
  FEATURE_FLAGS,
  TOKEN_KEY,
} from '../constants/appConstants';
import { FEATURE_FLAG_KEY_MAP, PERMISSIONS } from '../constants/authConstants';
import {
  GlobalState,
  IsValidPasswordPayload,
  IsValidPasswordResponse,
  LoginResponse,
  ResetPasswordResponse,
  ValidatePasswordResetPayload,
} from '../models/common';
import { ResetMFA } from '../models/reset-mfa.model';
import {
  ResetPasswordPayload,
  ValidateTokenParams,
} from '../models/reset-password.model';
import { Signup } from '../models/signup.model';
import { loginSuccess } from '../store/actions/globalStore.actions';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private twoFactorVerificationType$: BehaviorSubject<string> =
    new BehaviorSubject<string>('');

  private secretKey = '';

  private QRCodeImage = '';

  private signUpUserData: Signup = {} as Signup;

  private signUpPassword = '';

  private signUpToken = '';

  private ResetMFAResponse: ResetMFA = {} as ResetMFA;

  forgetEmail = '';

  newPassword = '';

  resetPasswordToken = '';

  authenticationType = '';

  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  isLogin: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  searchType: BehaviorSubject<string> = new BehaviorSubject<string>('');

  router = inject(Router);

  http = inject(HttpClient);

  cookieService = inject(CookieService);

  store = inject(Store<GlobalState>);

  static saveToken(token: string): void {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.setItem(TOKEN_KEY, token);
  }

  static saveAppFeatureFlags(flags: string) {
    localStorage.setItem(APP_FEATURE_FLAGS, flags);
  }

  static getToken(): string | null {
    return localStorage.getItem(TOKEN_KEY);
  }

  saveCsrfToken(csrf: any, csrfHash: any): void {
    if (!csrf) return;
    localStorage.setItem(CSRF, csrf);
    this.cookieService.set(CSRF_HASH, csrfHash);
  }

  getCsrfToken(): { csrfToken: string; csrfHash: string } {
    const csrfToken = localStorage.getItem(CSRF) || '';
    const csrfHash = this.cookieService.get(CSRF_HASH) || '';
    return { csrfToken, csrfHash };
  }

  getResetMFA(): ResetMFA {
    return this.ResetMFAResponse;
  }

  setResetMFA(value: ResetMFA) {
    this.ResetMFAResponse = value;
  }

  static getUser(): LoginResponse {
    const jwtToken = AuthService.getToken();
    const decodedToken: any = AuthService.getToken()
      ? jwtDecode(jwtToken as string)
      : null;
    return decodedToken;
  }

  static getRole() {
    const jwtToken = AuthService.getToken();
    const decodedToken: any = AuthService.getToken()
      ? jwtDecode(jwtToken as string)
      : null;
    return decodedToken ? decodedToken?.roles : null;
  }

  signOut(): void {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(CSRF);
    this.cookieService.delete(CSRF_HASH);
    this.store.dispatch(loginSuccess({ success: false }));
    this.router.navigate(['/']).then(() => {
      window.location.reload();
    });
  }

  authenticate(payload: LoginResponse): Observable<LoginResponse> | undefined {
    return this.http?.post<LoginResponse>(
      `${getBaseUrl()}/authenticateuser`,
      payload
    );
  }

  setForgetEmail(email: string) {
    this.forgetEmail = email;
  }

  getForgetEmail() {
    return this.forgetEmail;
  }

  setSignUpUserData(value: Signup) {
    this.signUpUserData = value;
  }

  getSignUpUserData() {
    return this.signUpUserData;
  }

  getSignUpPassword() {
    return this.signUpPassword;
  }

  getSignUpToken() {
    return this.signUpToken;
  }

  setSignUpToken(value: string) {
    this.signUpToken = value;
  }

  setSignUpPassword(value: string) {
    this.signUpPassword = value;
  }

  getTwoFactorVerificationType() {
    return this.twoFactorVerificationType$;
  }

  setTwoFactorVerificationType(value: string) {
    this.twoFactorVerificationType$.next(value);
  }

  getSecretKey(): string {
    return this.secretKey;
  }

  setSecretKey(value: string) {
    this.secretKey = value;
  }

  getQRCodeMFA(): string {
    return this.QRCodeImage;
  }

  setQRCodeMFA(value: string) {
    this.QRCodeImage = value;
  }

  static isAllowedAccess(action: string) {
    const permissions = PERMISSIONS;
    const userRoles = AuthService.getRole();

    const allowed = userRoles?.some((role: string) => {
      if (permissions[role]) {
        return permissions[role].find(permission => permission === action);
      }
      return null;
    });

    return Boolean(allowed);
  }

  static hasRole(role: string) {
    const userRoles = AuthService.getRole();
    return userRoles.some((userRole: string) => userRole === role);
  }

  static getCompany() {
    return AuthService.getUser()?.company;
  }

  authenticateResetUser(
    payload: ResetPasswordPayload
  ): Observable<ResetPasswordResponse> | undefined {
    return this.http?.post<ResetPasswordResponse>(
      `${getApiConstants().resetPassword.forgotPassword}`,
      payload
    );
  }

  setResetPasswordToken(value: string) {
    this.resetPasswordToken = value;
  }

  getNewPassword(): string {
    return this.newPassword;
  }

  setNewPassword(value: string) {
    this.newPassword = value;
  }

  getResetPasswordToken(): string {
    return this.resetPasswordToken;
  }

  setAuthenticationType(value: string) {
    this.authenticationType = value;
  }

  getAuthenticationType(): string {
    return this.authenticationType;
  }

  validateToken(params: ValidateTokenParams): Observable<any> | undefined {
    const httpParams = new HttpParams({ fromObject: params });

    return this.http?.get(getApiConstants().resetPassword.validateToken, {
      params: httpParams,
    });
  }

  validatePassword(value: string): Observable<boolean> | undefined {
    return this.http?.post<boolean>(
      getApiConstants().resetPassword.validatePassword,
      { password: value }
    );
  }

  updatePassword(payload: ResetPasswordPayload): Observable<any> | undefined {
    return this.http?.post(
      getApiConstants().resetPassword.updatePassword,
      payload
    );
  }

  static setFeatureFlags(flags: string[]) {
    const allKeys = [
      ...new Set([
        ...Object.values(FEATURE_FLAG_KEY_MAP),
        ...flags.filter(
          flag => !Object.keys(FEATURE_FLAG_KEY_MAP).includes(flag)
        ),
      ]),
    ];

    const featureFlagsArray = allKeys.map(key => ({
      key,
      active: flags.some(flag => {
        const resolvedKey = FEATURE_FLAG_KEY_MAP[flag] ?? flag;
        return resolvedKey === key;
      }),
    }));

    localStorage.setItem(FEATURE_FLAGS, JSON.stringify(featureFlagsArray));
  }

  static getFeatureFlags(): { key: string; active: boolean }[] {
    const featureFlags = localStorage.getItem(FEATURE_FLAGS);
    return featureFlags ? JSON.parse(featureFlags) : [];
  }

  static get appFeatureFlags() {
    return localStorage.getItem(APP_FEATURE_FLAGS);
  }

  isValidPassword(
    payload: IsValidPasswordPayload
  ): Observable<IsValidPasswordResponse> {
    return this.http?.post<IsValidPasswordResponse>(
      getApiConstants().resetPassword.isValidPassword,
      payload
    );
  }

  validatePasswordReset(
    payload: ValidatePasswordResetPayload
  ): Observable<IsValidPasswordResponse> {
    return this.http?.post<IsValidPasswordResponse>(
      getApiConstants().resetPassword.isResetPasswordValid,
      payload
    );
  }
}
