import './single-spa/asset-url';

import { enableProdMode, NgZone } from '@angular/core';
import 'zone.js/dist/zone';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { Router, NavigationStart } from '@angular/router';

import {
  singleSpaAngular,
  getSingleSpaExtraProviders,
} from 'single-spa-angular';

import { AppModule } from './app/app.module';
import { environment } from './environments/environment';
import { singleSpaPropsSubject } from './single-spa/single-spa-props';

if (environment.production) {
  enableProdMode();
}

const domElementGetter = () =>
  document.getElementById('ics-tms-app-v2') ??
  document.createElement('ics-tms-app-v2');

const lifecycles = singleSpaAngular({
  bootstrapFunction: singleSpaProps => {
    singleSpaPropsSubject.next(singleSpaProps);
    return platformBrowserDynamic(getSingleSpaExtraProviders()).bootstrapModule(
      AppModule
    );
  },
  domElementGetter,
  template: '<app-root />',
  Router,
  NavigationStart,
  NgZone,
});

export const { bootstrap, mount } = lifecycles;

export const unmount = [
  async () => {
    document.documentElement.classList.remove('is-view-full');
  },
  lifecycles.unmount,
];
