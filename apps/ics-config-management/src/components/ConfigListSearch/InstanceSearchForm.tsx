import React, {
  ChangeEvent,
  KeyboardEvent,
  memo,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Box, IconButton, Tooltip } from '@mui/material';
import ClickAwayListener from '@mui/base/ClickAwayListener';
import { Close as CloseIcon, Search as SearchIcon } from '@mui/icons-material';
import { useDebounce } from '../../hooks/useDebounce';
import { useToggle } from '../../hooks/useToggle';
import { SearchContainer, StyledInputBase } from './styles';
import { InstanceView } from './types';

type InstanceSearchFormProps = {
  defaultValue?: string;
  view: InstanceView;
  onSearchChange: (value: string) => void;
};

const InstanceSearchForm = memo(
  ({ defaultValue = '', view, onSearchChange }: InstanceSearchFormProps) => {
    const inputRef = useRef<HTMLInputElement>(null);
    const [instanceName, setInstanceName] = useState<string>(
      defaultValue ?? ''
    );
    const debouncedValue = useDebounce<string>(instanceName, 500);
    const [isFocused, toggleIsFocused, setFocused] = useToggle(
      !!instanceName.length
    );

    useEffect(() => {
      setInstanceName('');
      setFocused(false);
    }, [view]);

    useEffect(() => {
      if (defaultValue === null || defaultValue === '') {
        setInstanceName('');
      }
    }, [defaultValue]);

    const handleOnSearchCancel = useCallback(() => {
      setInstanceName('');
      toggleIsFocused();
      // Immediately trigger search change with empty value to clear results
      onSearchChange('');
    }, [setInstanceName, toggleIsFocused, onSearchChange]);

    const handleDebounce = useCallback(() => {
      const currentValue = debouncedValue?.trim() ?? '';
      onSearchChange(currentValue);
    }, [debouncedValue]);

    const handleSearchInputChange = useCallback(
      (event: ChangeEvent<HTMLInputElement>) => {
        const instanceNameValue = event.target.value;
        setInstanceName(instanceNameValue);
      },
      [setInstanceName]
    );

    const handleOnKeyUp = useCallback(
      (event: KeyboardEvent<HTMLInputElement>) => {
        const { key } = event;
        if (key === 'Escape') {
          handleOnSearchCancel();
        }
      },
      [handleOnSearchCancel]
    );

    const handleOnSearchClick = useCallback(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
      if (!isFocused) {
        toggleIsFocused();
      }
    }, [inputRef, isFocused, toggleIsFocused]);

    const handleOnClickOutside = useCallback(() => {
      if (!instanceName.length && isFocused) {
        handleOnSearchCancel();
      }
    }, [instanceName, isFocused, handleOnSearchCancel]);

    useEffect(() => {
      handleDebounce();
    }, [debouncedValue]);

    return (
      <ClickAwayListener onClickAway={handleOnClickOutside}>
        <SearchContainer
          key='InstanceSearchFormComponent-root'
          className='InstanceSearchFormComponent-root'
          sx={{
            width: isFocused ? '100%' : '42px',
          }}
        >
          <Box
            sx={{
              position: 'relative',
              height: '100%',
              flex: 1,
              width: isFocused ? '100%' : '0px',
            }}
          >
            <StyledInputBase
              inputProps={{
                ref: inputRef,
                'aria-label': 'Search',
                autoCapitalize: 'off',
                autoComplete: 'off',
                autoCorrect: 'off',
                spellCheck: 'false',
              }}
              onChange={handleSearchInputChange}
              value={instanceName}
              placeholder='Search'
              onKeyUp={handleOnKeyUp}
              type='search'
              sx={{
                width: isFocused ? '100%' : '0px',
              }}
            />
            {instanceName || isFocused ? (
              <Tooltip arrow title={instanceName ? 'Clear' : 'Close'}>
                <IconButton
                  onClick={handleOnSearchCancel}
                  sx={{
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    bottom: 0,
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </Tooltip>
            ) : null}
          </Box>
          <Tooltip arrow title='Search'>
            <IconButton
              onClick={handleOnSearchClick}
              sx={{
                flexShrink: 0,
              }}
            >
              <SearchIcon />
            </IconButton>
          </Tooltip>
        </SearchContainer>
      </ClickAwayListener>
    );
  },
  (prevProps, nextProps) =>
    prevProps.defaultValue === nextProps.defaultValue &&
    prevProps.onSearchChange === nextProps.onSearchChange
);

export default InstanceSearchForm;

export { InstanceSearchForm };
