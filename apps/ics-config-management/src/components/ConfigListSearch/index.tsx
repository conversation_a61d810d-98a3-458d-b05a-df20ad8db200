import React, { memo, useCallback, useMemo } from 'react';
import { Badge, Box, IconButton, Popover, Tooltip } from '@mui/material';
import PopupState, { bindPopover, bindTrigger } from 'material-ui-popup-state';
import {
  FilterAlt as FilterIcon,
  SortByAlpha as SortIcon,
} from '@mui/icons-material';
import type { DeviceConfig } from '../../constants/types';
import { safeJsonParse } from '../DynamicEditor/utils';
import { FilterContainer, StyledPopoverContent } from './styles';
import removeEmpty from './removeEmpty';
import { InstanceFilterForm } from './InstanceFilterForm';
import { InstanceSortForm } from './InstanceSortForm';
import { InstanceSearchForm } from './InstanceSearchForm';
import safeBase64decode from './safeBase64decode';
import {
  UserOptions,
  InstanceSortOrder,
  InstanceSortOrderBy,
  InstanceView,
} from './types';
import ViewToggleButtonGroup from './ViewToggleButtonGroup';

type ConfigListSearchProps = {
  deviceConfigs: DeviceConfig[];
  view: InstanceView;
  handleViewChange: (
    event: React.MouseEvent<HTMLElement>,
    view: InstanceView
  ) => void;
  // State values from parent
  appDescriptorId: string;
  configFileId: string;
  createdBy: string;
  editedBy: string;
  instanceName: string;
  order: InstanceSortOrder;
  orderBy: InstanceSortOrderBy;
  // State setter from parent
  onStateChange: (params: Record<string, string>) => void;
  // Clear filters callback
  onClearFilters: () => void;
};

const ConfigListSearch = memo(
  ({
    deviceConfigs,
    view,
    handleViewChange,
    appDescriptorId: parentAppDescriptorId,
    configFileId: parentConfigFileId,
    createdBy: parentCreatedBy,
    editedBy: parentEditedBy,
    instanceName: parentInstanceName,
    order: parentOrder,
    orderBy: parentOrderBy,
    onStateChange,
    onClearFilters,
  }: ConfigListSearchProps) => {

    const {
      appDescriptorId,
      configFileId,
      createdBy,
      editedBy,
      instanceName,
      order,
      orderBy,
    } = useMemo(
      () => ({
        appDescriptorId:
          view === InstanceView.ALL_INSTANCES
            ? parentAppDescriptorId
            : null,
        configFileId:
          view === InstanceView.ALL_INSTANCES
            ? parentConfigFileId
            : null,
        createdBy: safeJsonParse(safeBase64decode(parentCreatedBy))
          .data as UserOptions,
        editedBy: safeJsonParse(safeBase64decode(parentEditedBy))
          .data as UserOptions,
        instanceName: parentInstanceName,
        order: parentOrder as InstanceSortOrder,
        orderBy: parentOrderBy as InstanceSortOrderBy,
      }),
      [
        view,
        parentAppDescriptorId,
        parentConfigFileId,
        parentCreatedBy,
        parentEditedBy,
        parentInstanceName,
        parentOrder,
        parentOrderBy,
      ]
    );

    const filterDefaultValues = useMemo(
      () => ({
        appDescriptorId,
        configFileId,
        createdBy,
        editedBy,
      }),
      [appDescriptorId, configFileId, createdBy, editedBy]
    );



    const sortDefaultValues = useMemo(
      () => ({
        order,
        orderBy,
      }),
      [order, orderBy]
    );

    const resetValues = useMemo(
      () => ({
        orderBy:
          view === InstanceView.ALL_INSTANCES
            ? InstanceSortOrderBy.lastUpdated
            : InstanceSortOrderBy.name,
        order:
          view === InstanceView.ALL_INSTANCES
            ? InstanceSortOrder.desc
            : InstanceSortOrder.asc,
      }),
      [view]
    );

    const badgeNumber = useMemo(() => {
      const searchObject = removeEmpty({
        appDescriptorId,
        configFileId,
        createdBy: createdBy?.id,
        editedBy: editedBy?.id,
      });
      return Object.keys(searchObject).length;
    }, [appDescriptorId, configFileId, createdBy, editedBy]);

    const handleFilterOrSortSubmit = useCallback(
      (data: Record<string, any>) => {
        // Only pass the changed data, let the parent merge with existing state
        const searchObject = removeEmpty({
          ...data,
          pageNumber: '', // Always reset page when filters/sort change
        });
        onStateChange(searchObject);
      },
      [onStateChange]
    );

    const handleSearchSubmit = useCallback(
      (value: string) => {
        const newValue = value?.trim();
        // Pass instanceName in state change to trigger API call
        // The URL filtering logic will handle whether to include it in the URL
        const searchObject = {
          instanceName: newValue?.length >= 3 ? newValue : '',
          pageNumber: '', // Always reset page when search changes
        };

        onStateChange(searchObject);
      },
      [onStateChange]
    );

    return (
      <Box display='flex' alignItems='center' gap={3}>
        <Box display='flex' alignItems='center' gap={1}>
          <InstanceSearchForm
            defaultValue={instanceName}
            view={view}
            onSearchChange={handleSearchSubmit}
          />

          <PopupState variant='popover' popupId='config-instance-filter'>
            {popupState => (
              <>
                <FilterContainer
                  sx={{
                    backgroundColor: popupState.isOpen
                      ? 'common.lightSurface'
                      : 'transparent',
                  }}
                >
                  <Badge
                    badgeContent={badgeNumber}
                    color='primary'
                    sx={{
                      '& .MuiBadge-badge': {
                        top: 4,
                        right: 4,
                      },
                    }}
                  >
                    <Tooltip arrow title='Filter Options'>
                      <IconButton
                        data-testid='config-instance-filter-btn'
                        {...bindTrigger(popupState)}
                      >
                        <FilterIcon />
                      </IconButton>
                    </Tooltip>
                  </Badge>
                </FilterContainer>
                {Boolean(deviceConfigs?.length) && (
                  <Popover
                    {...bindPopover(popupState)}
                    anchorOrigin={{
                      vertical: 'bottom',
                      horizontal: 'center',
                    }}
                    transformOrigin={{
                      vertical: 'top',
                      horizontal: 'right',
                    }}
                    PaperProps={{
                      sx: {
                        bgcolor: 'transparent',
                        mt: '13px',
                        ml: '79px',
                        overflow: 'visible',
                      },
                    }}
                  >
                    <StyledPopoverContent
                      sx={{
                        '&::before': {
                          right: '70px',
                        },
                        '&::after': {
                          right: '70px',
                        },
                      }}
                    >
                      <InstanceFilterForm
                        view={view}
                        defaultValues={filterDefaultValues}
                        deviceConfigs={deviceConfigs}
                        onFormSubmit={handleFilterOrSortSubmit}
                        onClearFilters={onClearFilters}
                        popupState={popupState}
                      />
                    </StyledPopoverContent>
                  </Popover>
                )}
              </>
            )}
          </PopupState>

          <PopupState variant='popover' popupId='config-instance-sort'>
            {popupState => (
              <>
                <FilterContainer
                  sx={{
                    backgroundColor: popupState.isOpen
                      ? 'common.lightSurface'
                      : 'transparent',
                  }}
                >
                  <Badge
                    badgeContent={order === InstanceSortOrder.desc ? '↓' : '↑'}
                    color='primary'
                    sx={{
                      '& .MuiBadge-badge': {
                        top: 4,
                        right: 4,
                      },
                    }}
                  >
                    <Tooltip arrow title='Sort Options'>
                      <IconButton
                        data-testid='config-instance-sort-btn'
                        {...bindTrigger(popupState)}
                      >
                        <SortIcon />
                      </IconButton>
                    </Tooltip>
                  </Badge>
                </FilterContainer>
                {Boolean(deviceConfigs?.length) && (
                  <Popover
                    {...bindPopover(popupState)}
                    anchorOrigin={{
                      vertical: 'bottom',
                      horizontal: 'center',
                    }}
                    transformOrigin={{
                      vertical: 'top',
                      horizontal: 'right',
                    }}
                    PaperProps={{
                      sx: {
                        bgcolor: 'transparent',
                        mt: '42px',
                        ml: '30px',
                        overflow: 'visible',
                      },
                    }}
                  >
                    <StyledPopoverContent>
                      <InstanceSortForm
                        defaultValues={sortDefaultValues}
                        onFormSubmit={handleFilterOrSortSubmit}
                        popupState={popupState}
                        resetValues={resetValues}
                      />
                    </StyledPopoverContent>
                  </Popover>
                )}
              </>
            )}
          </PopupState>
        </Box>

        <ViewToggleButtonGroup
          view={view}
          handleViewChange={handleViewChange}
        />
      </Box>
    );
  },
  (prevProps, nextProps) =>
    prevProps.deviceConfigs === nextProps.deviceConfigs &&
    prevProps.view === nextProps.view &&
    prevProps.handleViewChange === nextProps.handleViewChange
);

export default ConfigListSearch;

export { ConfigListSearch };
