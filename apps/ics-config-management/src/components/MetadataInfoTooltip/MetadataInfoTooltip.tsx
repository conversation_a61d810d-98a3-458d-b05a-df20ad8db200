import React from 'react';
import { Tooltip, Box, Typography, CircularProgress } from '@mui/material';
import { makeStyles } from '@mui/styles';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { toDeploymentDate } from '../../utils/helpers';

const useStyles = makeStyles(() => ({
  arrow: {
    '&:before': {
      border: '1px solid #E6E8ED',
    },
    color: 'white',
  },
  tooltip: {
    backgroundColor: 'white',
    border: '1px solid #E6E8ED',
    color: '#4A4A4A',
    maxWidth: 'none',
    padding: '12px 16px',
  },
  infoIcon: {
    fontSize: '16px',
    color: '#6B7280',
    cursor: 'pointer',
    '&:hover': {
      color: '#374151',
    },
  },
}));

interface MetadataUser {
  $id: string;
  fullName?: string;
  email?: string;
}

interface MetadataInfo {
  created?: {
    date: string;
    user: MetadataUser;
  };
  edited?: {
    date: string;
    user: MetadataUser;
  };
}

interface MetadataInfoTooltipProps {
  metadata?: MetadataInfo;
  isLoading?: boolean;
  children?: React.ReactNode;
}

const MetadataRow = ({
  label,
  user,
  date,
}: {
  label: string;
  user?: MetadataUser;
  date?: string;
}) => (
  <Box mb={1}>
    <Typography variant='caption' sx={{ fontWeight: 600, color: '#374151' }}>
      {label}
    </Typography>
    <Typography variant='body2' sx={{ color: '#6B7280', fontSize: '12px' }}>
      {user?.fullName || 'Unknown User'}
    </Typography>
    <Typography variant='body2' sx={{ color: '#6B7280', fontSize: '12px' }}>
      {date ? toDeploymentDate(date) : 'Unknown Date'}
    </Typography>
  </Box>
);

const LoadingContent = () => (
  <Box display='flex' alignItems='center' gap={1}>
    <CircularProgress size={16} />
    <Typography variant='body2' sx={{ color: '#6B7280' }}>
      Loading metadata...
    </Typography>
  </Box>
);

const MetadataContent = ({ metadata }: { metadata: MetadataInfo }) => {
  if (!metadata?.created && !metadata?.edited) {
    return (
      <Typography variant='body2' sx={{ color: '#6B7280' }}>
        No metadata available
      </Typography>
    );
  }

  return (
    <Box minWidth={200}>
      {metadata.created && (
        <MetadataRow
          label='Created By'
          user={metadata.created.user}
          date={metadata.created.date}
        />
      )}
      {metadata.edited && (
        <MetadataRow
          label='Edited By'
          user={metadata.edited.user}
          date={metadata.edited.date}
        />
      )}
    </Box>
  );
};

const MetadataInfoTooltip = ({
  metadata,
  isLoading = false,
  children,
}: MetadataInfoTooltipProps) => {
  const classes = useStyles();

  const tooltipContent = isLoading ? (
    <LoadingContent />
  ) : (
    <MetadataContent metadata={metadata} />
  );

  return (
    <Tooltip
      title={tooltipContent}
      arrow
      classes={{ arrow: classes.arrow, tooltip: classes.tooltip }}
      enterDelay={300}
      leaveDelay={200}
    >
      <span>
        {children || <InfoOutlinedIcon className={classes.infoIcon} />}
      </span>
    </Tooltip>
  );
};

export default MetadataInfoTooltip;
export type { MetadataInfo, MetadataUser };
