import { useCallback, useEffect, useState } from 'react';
import useEventListener from './useEventListener';

type SerializableValue = string | number | boolean | null | undefined;
type SerializableObject = Record<string, SerializableValue>;

interface UsePersistedStateOptions {
  /**
   * Storage key prefix to avoid conflicts
   */
  keyPrefix?: string;
  /**
   * Storage type - localStorage persists across sessions, sessionStorage only for current session
   */
  storageType?: 'localStorage' | 'sessionStorage';
  /**
   * Whether to sync state across tabs/windows
   */
  syncAcrossTabs?: boolean;
}

/**
 * Custom hook for persisting state in browser storage with cross-tab synchronization
 * 
 * @param key - Storage key (will be prefixed if keyPrefix is provided)
 * @param initialValue - Initial value if no stored value exists
 * @param options - Configuration options
 * @returns [storedValue, setValue, clearValue]
 */
function usePersistedState<T extends SerializableObject>(
  key: string,
  initialValue: T,
  options: UsePersistedStateOptions = {}
): [T, (value: T | ((prev: T) => T)) => void, () => void] {
  const {
    keyPrefix = 'ics-config-management',
    storageType = 'localStorage',
    syncAcrossTabs = true,
  } = options;

  const storageKey = keyPrefix ? `${keyPrefix}:${key}` : key;

  const getStorage = useCallback(() => {
    if (typeof window === 'undefined') return null;
    return storageType === 'localStorage' ? localStorage : sessionStorage;
  }, [storageType]);

  const readValue = useCallback((): T => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const storage = getStorage();
      if (!storage) return initialValue;

      const item = storage.getItem(storageKey);
      if (item === null) return initialValue;

      const parsed = JSON.parse(item);
      
      // Merge with initial value to ensure all expected keys exist
      return { ...initialValue, ...parsed };
    } catch (error) {
      console.warn(`Error reading from ${storageType}:`, error);
      return initialValue;
    }
  }, [storageKey, initialValue, getStorage, storageType]);

  const [storedValue, setStoredValue] = useState<T>(readValue);

  const setValue = useCallback(
    (value: T | ((prev: T) => T)) => {
      try {
        const storage = getStorage();
        if (!storage) return;

        const valueToStore = value instanceof Function ? value(storedValue) : value;
        
        // Filter out empty/null values to keep storage clean
        const cleanedValue = Object.entries(valueToStore).reduce((acc, [k, v]) => {
          if (v !== '' && v !== null && v !== undefined) {
            acc[k] = v;
          }
          return acc;
        }, {} as Record<string, SerializableValue>);

        setStoredValue(valueToStore);
        storage.setItem(storageKey, JSON.stringify(cleanedValue));

        // Dispatch custom event for cross-tab sync if enabled
        if (syncAcrossTabs) {
          window.dispatchEvent(
            new CustomEvent('persistedStateChange', {
              detail: { key: storageKey, value: cleanedValue },
            })
          );
        }
      } catch (error) {
        console.warn(`Error writing to ${storageType}:`, error);
      }
    },
    [storageKey, storedValue, getStorage, storageType, syncAcrossTabs]
  );

  const clearValue = useCallback(() => {
    try {
      const storage = getStorage();
      if (!storage) return;

      storage.removeItem(storageKey);
      setStoredValue(initialValue);

      if (syncAcrossTabs) {
        window.dispatchEvent(
          new CustomEvent('persistedStateChange', {
            detail: { key: storageKey, value: null },
          })
        );
      }
    } catch (error) {
      console.warn(`Error clearing from ${storageType}:`, error);
    }
  }, [storageKey, initialValue, getStorage, storageType, syncAcrossTabs]);

  // Handle storage events for cross-tab synchronization
  const handleStorageChange = useCallback(
    (event: StorageEvent | CustomEvent) => {
      if (!syncAcrossTabs) return;

      if (event instanceof StorageEvent) {
        // Native storage event (cross-tab)
        if (event.key === storageKey) {
          setStoredValue(readValue());
        }
      } else if (event instanceof CustomEvent) {
        // Custom event (same tab)
        const { key: eventKey } = event.detail;
        if (eventKey === storageKey) {
          setStoredValue(readValue());
        }
      }
    },
    [storageKey, readValue, syncAcrossTabs]
  );

  // Listen for storage changes if cross-tab sync is enabled
  useEventListener('storage', handleStorageChange);
  useEventListener('persistedStateChange', handleStorageChange);

  // Initialize state on mount
  useEffect(() => {
    setStoredValue(readValue());
  }, [readValue]);

  return [storedValue, setValue, clearValue];
}

export default usePersistedState;
