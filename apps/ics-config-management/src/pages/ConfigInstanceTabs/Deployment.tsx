/* eslint-disable import/no-cycle */
import { Box, CircularProgress, Typography } from '@mui/material';
import filter from 'lodash/filter';
import React, { FC, useCallback, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useSnackbar } from 'notistack';

import { useMergeState } from '../../hooks/useMergeStates';
import DeploymentAccordionGrid from '../../components/DeploymentAccordion/DeploymentAccordionGrid';
import DeploymentDialog from '../../components/DeploymentDialog';
import {
  ConfigDeploymentResponse,
  DeploymentStatus,
} from '../../constants/types';
import { useGetDeployments } from '../../services/use-query';

const Deployment: FC<{ id: string }> = ({ id }) => {
  const {
    data: deployments,
    isLoading,
    refetch: getDeployments,
  } = useGetDeployments(id);
  interface States {
    isModalOpen: boolean;
    deploymentId: string;
    deploymentDate: string;
  }
  const queryClient = useQueryClient();
  const { enqueueSnackbar } = useSnackbar();

  useEffect(() => {
    queryClient.invalidateQueries(['getDeployments']);
    queryClient.invalidateQueries(['getDeploymentStats']);
  }, []);

  const [{ isModalOpen, deploymentId, deploymentDate }, setStates] =
    useMergeState<States>({
      isModalOpen: false,
      deploymentId: '',
      deploymentDate: '',
    });

  const filterByDeploymentStatus = (status: string) =>
    filter(
      deployments,
      (deployment: ConfigDeploymentResponse) =>
        status === deployment.deploymentStatus
    );

  const onRowClick = useCallback(
    (deploymentID: string, deploymentDATE: string) => {
      setStates({
        deploymentId: deploymentID,
        deploymentDate: deploymentDATE,
        isModalOpen: true,
      });
      // eslint-disable-next-line react-hooks/exhaustive-deps
    },
    []
  );

  return (
    <Box
      data-testid='DeploymentPage'
      p={2}
      bgcolor='common.backgroundLight'
      height={1}
      display='flex'
      minHeight={0}
    >
      <Box
        bgcolor='common.white'
        flex={1}
        display='flex'
        flexDirection='column'
        gap={3}
        p={3}
        borderRadius='16px'
        overflow='auto'
      >
        <Typography variant='titleLarge'>Deployments</Typography>
        <Box pb='10px'>
          {isLoading && (
            <Box
              width='100%'
              height='100%'
              display='flex'
              justifyContent='center'
              alignItems='center'
            >
              <CircularProgress />
            </Box>
          )}
          {!isLoading && (
            <>
              <Box>
                <DeploymentAccordionGrid
                  data-testid='ActiveDeploymentList'
                  title='Active deployments'
                  instanceId={id}
                  deployments={filterByDeploymentStatus(
                    DeploymentStatus.Active
                  )}
                  onRowClick={onRowClick}
                />
              </Box>
              <Box>
                <DeploymentAccordionGrid
                  data-testid='PastDeploymentList'
                  title='Past deployments'
                  instanceId={id}
                  deployments={filterByDeploymentStatus(
                    DeploymentStatus.Completed
                  )}
                  onRowClick={onRowClick}
                />
              </Box>
              <DeploymentDialog
                isOpen={isModalOpen}
                id={id}
                deploymentId={deploymentId}
                deploymentDate={deploymentDate}
                refetch={getDeployments}
                onClose={() => {
                  setStates({ isModalOpen: false });
                }}
                onError={() => {
                  setStates({ isModalOpen: false });
                  enqueueSnackbar('Failed retrieve deployment detail', {
                    variant: 'error',
                  });
                }}
              />
            </>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default Deployment;
