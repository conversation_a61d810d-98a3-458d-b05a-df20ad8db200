/* eslint-disable import/no-cycle */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useSnackbar } from 'notistack';
import pluralize from 'pluralize';
import {
  Box,
  CircularProgress,
  Typography,
  Button,
  Drawer,
  Tabs,
  Tab,
} from '@mui/material/';
import { useQueryClient } from '@tanstack/react-query';
import { CheckCircle, ExclamationTriangle } from 'react-bootstrap-icons';
import { useEffectOnce } from 'usehooks-ts';
import AssignmentDialog from '../../components/AssignmentDialog';
import ConfirmDialog from '../../components/ConfirmDialog';
import {
  AssignmentItem,
  HierarchyLevelEnum,
  Row,
  RevisionPublishDeploymentType,
  InstanceDetailsResponse,
} from '../../constants/types';
import { useMergeState } from '../../hooks/useMergeStates';
import {
  useGetAssignments,
  useGetTopLevelAssignmentStats,
  useGetRevisions,
} from '../../services/use-query';
import { getDecodeToken, hasFeatureFlag } from '../../utils/helpers';
import useGlobal from '../../hooks/useContext';
import FeatureFlags from '../../constants/featureFlags';
import AssignAndDeployHeader from './AssignAndDeployHeader';

const LEVELS = HierarchyLevelEnum;
type AssignmentInLevel = {
  [key in HierarchyLevelEnum]: AssignmentItem[];
};
type Entry<T> = { [K in keyof T]: [K, T[K]] }[keyof T];
type AssignmentEntires = Entry<AssignmentInLevel>;

interface States {
  selectedAssignments?: AssignmentInLevel;
  initAssignments?: AssignmentInLevel;
  level?: HierarchyLevelEnum;
  showAssignmentPage: boolean;
  isReadOnly: boolean;
  isPosting: boolean;
  openingTableData?: Row[];
  openingTableInitData?: Row[];
  isFetching?: boolean;
  isInitRendered?: boolean;
  isAddToAssignmentModalOpen: boolean;
  isLoading: boolean;
  isPlusButtonClicked: boolean;
}

const Assignment = ({
  id,
  instanceDetails,
  isVisible,
  onClose,
  handleShowAssignment,
}: {
  id: string;
  instanceDetails: Object;
  isVisible;
  onClose;
  handleShowAssignment?: (
    value1: string,
    value2: InstanceDetailsResponse
  ) => void;
}) => {
  const [
    {
      selectedAssignments,
      initAssignments,
      level,
      showAssignmentPage,
      isReadOnly,
      isInitRendered,
      isAddToAssignmentModalOpen,
      isLoading,
      isPlusButtonClicked,
    },
    setStates,
  ] = useMergeState<States>({
    showAssignmentPage: false,
    isReadOnly: true,
    openingTableData: [],
    openingTableInitData: [],
    isInitRendered: false,
    isAddToAssignmentModalOpen: false,
    isLoading: true,
    isPlusButtonClicked: false,
    isPosting: false,
  });

  const { enqueueSnackbar } = useSnackbar();
  const { updateDeployable } = useGlobal();

  const [focusComponent, setFocusComponent] = React.useState('');
  const [isCancelClicked, setIsCancelClicked] = useState(false);
  const resetAssignmentData = {
    Tenant: [],
    SiteTag: [],
    Site: [],
    Device: [],
  };

  const queryClient = useQueryClient();

  const {
    data: topLevelAssignmentStats,
    isLoading: isStatLoading,
    isError: isStatLoadingError,
  } = useGetTopLevelAssignmentStats(id);
  const { data: allAssignments, refetch: refetchAssignments } =
    useGetAssignments(id);
  const { data: dataRevisions } = useGetRevisions(id);
  const [allDevices, setAllDevices] = useState([]);
  const [allSites, setAllSites] = useState([]);
  const [allSiteTags, setAllSiteTags] = useState([]);
  const [selectedTab, setSelectedTab] = useState('Tenant');
  const hasMfaAccess = hasFeatureFlag(FeatureFlags.CONFIG_MGMT_DEPLOY_WITH_MFA);

  const reFetchConfigInstanceStat = async () => {
    await queryClient.invalidateQueries(['getConfigInstanceStat']);
  };

  const reFetchAssignmentStats = async () => {
    await queryClient.invalidateQueries(['getConfigInstanceAssignmentStats']);
  };

  // inject checked props to row data
  const formatTableData = (
    assets: Row[],
    selectedAssets: AssignmentItem[]
  ): Row[] => {
    // inject assets props
    // inject checked props
    // if assignmentValue exist, checked = true
    const values = assets?.map(asset => {
      const matchAsset = selectedAssets.find(
        selectedAsset => selectedAsset.assignmentValue === asset?.id?.toString()
      );
      return { ...matchAsset, ...asset, checked: matchAsset !== undefined };
    });
    return values || [];
  };

  const isAssignmentsEmpty = useMemo(() => {
    if (allAssignments) {
      return Object.entries(allAssignments).every(item => {
        if (item[1].length > 0) {
          return false;
        }
        return true;
      });
    }
    return true;
  }, [allAssignments]);

  useEffect(() => {
    if (allAssignments) {
      const calculateTenant = () => {
        const decodedToken = getDecodeToken();
        if (allAssignments.Tenant.length) {
          const value = allAssignments.Tenant.map(tag => ({
            ...tag,
            assignmentName: decodedToken.company.name,
          }));
          return value;
        }
        return allAssignments.Tenant;
      };

      const newTenant = calculateTenant();

      const newData = {
        Site: allAssignments?.Site,
        SiteTag: allAssignments?.SiteTag,
        Device: allAssignments?.Device,
        Tenant: newTenant,
      };

      if (!isInitRendered) {
        setStates({
          showAssignmentPage: !isAssignmentsEmpty,
          isInitRendered: true,
          selectedAssignments: newData,
          initAssignments: newData,
          isLoading: false,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [allAssignments]);

  useEffect(() => {
    setStates({
      isInitRendered: false,
    });
  }, [allAssignments, setStates]);

  const onIconClick = useCallback(async (title: HierarchyLevelEnum) => {
    try {
      setFocusComponent(title);
      const titleType = pluralize(title, 1).replace(/ /g, '');
      setStates({
        isAddToAssignmentModalOpen: true,
        level: titleType,
        isPlusButtonClicked: true,
      });
    } catch (err) {
      setStates({ isAddToAssignmentModalOpen: false });
    }
  }, []);

  useEffectOnce(() => {
    onIconClick(HierarchyLevelEnum.Tenant);
  });

  const handleTabChange = newTab => {
    setStates({
      isPlusButtonClicked: false,
    });
    setSelectedTab(newTab);
    onIconClick(newTab);
  };

  // get full asset list by level
  const clickedDialogRows: Row[] = useMemo(() => {
    switch (level) {
      case HierarchyLevelEnum.SiteTag:
        return allSiteTags;
      case HierarchyLevelEnum.Site:
        return allSites;
      case HierarchyLevelEnum.Device:
        return allDevices;
      case HierarchyLevelEnum.Tenant:
        try {
          const decodedToken = getDecodeToken();
          return [
            {
              assignmentValue: decodedToken.company.id,
              name: decodedToken.company.name,
              id: decodedToken.company.id,
            },
          ];
        } catch (err) {
          enqueueSnackbar('Failed retrieve company detail', {
            variant: 'error',
          });
        }
        return [];
      default:
        return [];
    }
  }, [allDevices, allSiteTags, allSites, level]);

  const retrieveAllAssetByType = useCallback(() => {
    try {
      const formattedInitData = formatTableData(
        clickedDialogRows,
        initAssignments[level]
      );
      const formattedData = formatTableData(
        clickedDialogRows,
        selectedAssignments[level]
      );
      return { formattedData, formattedInitData };
    } catch (err) {
      setStates({ isAddToAssignmentModalOpen: false });
    }
    return null;
  }, [level, initAssignments, selectedAssignments]);

  const handleClose = useCallback(() => {
    setStates({
      isAddToAssignmentModalOpen: false,
      isPlusButtonClicked: false,
    });
  }, []);

  // update selected assignments
  const updateAssignments = useCallback(
    (newLevel: HierarchyLevelEnum) => (newAssignments: AssignmentItem[]) => {
      if (Object.values(LEVELS).includes(newLevel)) {
        setStates({
          selectedAssignments: {
            ...selectedAssignments,
            [newLevel]: newAssignments,
          },
        });
      }
    },
    [selectedAssignments]
  );

  const clearAssignments = useCallback(
    (newAssignments: AssignmentItem[]) => {
      if (newAssignments.length === 0) {
        setStates({
          selectedAssignments: {
            [LEVELS.Tenant]: [],
            [LEVELS.SiteTag]: [],
            [LEVELS.Site]: [],
            [LEVELS.Device]: [],
          },
        });
      }
    },
    [selectedAssignments]
  );

  const checkCMPermission = () => {
    try {
      const decodedToken = getDecodeToken();
      return !decodedToken?.roles?.some(item => item === 'CONFIG_MGMT_ASSIGN');
    } catch (err) {
      enqueueSnackbar('Failed retrieve permission detail', {
        variant: 'error',
      });
    }
    return null;
  };

  const hierarchyValidation = useCallback(() => {
    const isTenantSelected = selectedAssignments?.Tenant?.length !== 0;
    const newObject = { ...resetAssignmentData };
    if (isTenantSelected) {
      const tenantObject = initAssignments?.Tenant?.length
        ? initAssignments.Tenant
        : [{ assignmentValue: getDecodeToken().company.id }];

      newObject.Tenant = tenantObject;
    }
    const siteIdList = selectedAssignments?.Site?.map(selectedSite => {
      const found = allSites?.find(
        site => selectedSite.assignmentValue === site.id.toString()
      );
      return { ...found, ...selectedSite };
    });

    const newSiteTags =
      selectedAssignments?.SiteTag?.map(selectedTag => {
        const found = allSiteTags?.find(
          tag => selectedTag.assignmentValue === tag.id.toString()
        );
        return { ...found, ...selectedTag };
      }) ?? [];

    const newDevices = selectedAssignments?.Device?.map(selectedDevice => {
      const found = allDevices?.find(
        device => selectedDevice.assignmentValue === device.id.toString()
      );
      return { ...found, ...selectedDevice };
    });

    newObject.Device = newDevices;
    newObject.Site = siteIdList;
    newObject.SiteTag = newSiteTags;
    return newObject;
  }, [selectedAssignments]);

  const getFinalizeData = useCallback(
    data => {
      const array = (
        Object.entries(data) as unknown as AssignmentEntires[]
      ).map(([thisLevel, assignmentsByLevel]) => {
        const finalAssignments = assignmentsByLevel?.map(item => ({
          ...(item?.assignmentId && { assignmentId: item.assignmentId }),
          assignmentValue: item.assignmentValue.toString(),
        }));
        return { hierarchyLevel: thisLevel, assignments: finalAssignments };
      });
      const finalObjectSharp = {
        configAssignments: array,
      };
      return finalObjectSharp;
    },
    [selectedAssignments]
  );

  const assignments = useMemo(() => {
    const hierarchyData = hierarchyValidation();
    return getFinalizeData(hierarchyData);
  }, [selectedAssignments]);

  const handleOnSubmit = useCallback(async () => {
    updateDeployable(undefined);
    setStates({
      isReadOnly: true,
    });
    await reFetchConfigInstanceStat();
    await reFetchAssignmentStats();
    await queryClient.invalidateQueries(['getInstances']);
    await refetchAssignments();

    onClose();
  }, []);

  const createEntitiesList = data => {
    const entities = [];
    if (level === 'Device') {
      data.forEach(device => {
        const found = allDevices.some(entry => entry.id === device.id);
        if (!found) entities.push(device);
      });
      setAllDevices([...allDevices, ...entities]);
    }
    if (level === 'Site') {
      data.forEach(device => {
        const found = allSites.some(entry => entry.id === device.id);
        if (!found) entities.push(device);
      });
      setAllSites([...allSites, ...entities]);
    }
    if (level === 'SiteTag') {
      data.forEach(device => {
        const found = allSiteTags.some(entry => entry.id === device.id);
        if (!found) entities.push(device);
      });
      setAllSiteTags([...allSiteTags, ...entities]);
    }
  };

  return (
    <Drawer
      anchor='right'
      hideBackdrop
      open={isVisible}
      onClose={() => {
        onClose();
      }}
      PaperProps={{
        sx: {
          width: 900,
          borderRadius: '10px',
          bgcolor: 'common.backgroundLight',
        },
      }}
    >
      <AssignAndDeployHeader
        instanceDetails={instanceDetails}
        setIsDrawerOpen={() => onClose()}
        handleShowAssignment={handleShowAssignment}
      />
      <Box
        data-testid='AssignmentPage'
        p={2}
        height='100vh'
        display='flex'
        minHeight={0}
        flexDirection='column'
      >
        {!showAssignmentPage && (
          <Box
            flex={1}
            display='flex'
            flexDirection='column'
            gap={3}
            p={3}
            borderRadius='16px'
          >
            <Box
              display='flex'
              flexDirection='row'
              justifyContent='space-between'
            >
              <Typography variant='titleLarge'>No Assignment</Typography>
              <Button
                disabled={checkCMPermission() || !dataRevisions?.totalCount}
                sx={{ m: 0 }}
                variant='outlined'
                onClick={() => {
                  setStates({ showAssignmentPage: true, isReadOnly: false });
                }}
              >
                Edit Assignment
              </Button>
            </Box>
            <Typography variant='bodyMedium' width='400px'>
              Assign this config instance to a tenant or specific site tags,
              sites and devices. Any applicable assets that fall into the
              resulting assignment will be deployed to when you trigger a
              deployment.
            </Typography>
          </Box>
        )}
        {isLoading && (
          <Box
            width='100%'
            height='100%'
            display='flex'
            justifyContent='center'
            alignItems='center'
          >
            <CircularProgress />
          </Box>
        )}
        {showAssignmentPage && (
          <Box flexShrink={1} minHeight={0} width='100%'>
            <Box
              bgcolor='common.backgroundLight'
              p='10px'
              borderRadius='16px'
              gap={3}
              display='grid'
            >
              <Box
                flexDirection='row'
                display='flex'
                alignItems='center'
                justifyContent='space-between'
              >
                {isStatLoading && (
                  <Box>
                    <Typography variant='titleLarge'>
                      {isReadOnly ? 'Assets in assignment' : 'Edit Assignment'}
                    </Typography>
                    <Box>
                      {/* Todo: display progress everytime stats is refetched */}
                      <CircularProgress
                        sx={{ color: 'common.icon' }}
                        size='12px'
                      />
                    </Box>
                  </Box>
                )}

                {isStatLoadingError && (
                  <Box>
                    <Typography variant='titleLarge'>
                      {isReadOnly ? 'Assets in assignment' : 'Edit Assignment'}
                    </Typography>
                    <Box>
                      <Typography
                        variant='labelSmall'
                        sx={{ color: '#ff0000' }}
                      >
                        Error
                      </Typography>
                    </Box>
                  </Box>
                )}

                {!isStatLoading && !isStatLoadingError && (
                  <Box
                    display='flex'
                    flexDirection='column'
                    sx={{
                      position: 'relative',
                      bottom: '30%',
                      height: 'auto',
                    }}
                  >
                    <Typography variant='h6'>
                      {isReadOnly
                        ? `${
                            topLevelAssignmentStats.applicable +
                            topLevelAssignmentStats.overridden
                          } Assets in assignment`
                        : 'Edit Assignment'}
                    </Typography>
                    <Box
                      flexDirection='row'
                      display='flex'
                      gap={2}
                      mt='10px'
                      color='#5D5D67'
                    >
                      <CheckCircle color='#05A952' fontSize='large' />
                      <Typography variant='body2'>
                        {topLevelAssignmentStats.applicable} Applicable
                      </Typography>
                      <ExclamationTriangle color='#F56733' fontSize='large' />
                      <Typography variant='body2'>
                        {topLevelAssignmentStats.overridden} Overridden
                      </Typography>
                    </Box>
                  </Box>
                )}
              </Box>

              <Box
                sx={{
                  position: 'absolute',
                  right: '25px',
                  borderBottom: 1,
                  borderTop: 1,
                  borderColor: 'divider',
                  width: '401px',
                  border: '1px solid #ddd',
                  borderRadius: '8px',
                }}
              >
                <Tabs
                  value={selectedTab}
                  onChange={newTab => handleTabChange(newTab)}
                  variant='scrollable'
                  scrollButtons='auto'
                  TabIndicatorProps={{
                    style: { display: 'none' },
                  }}
                >
                  <Tab
                    label={`${HierarchyLevelEnum.Tenant}`}
                    onClick={() => {
                      handleTabChange(HierarchyLevelEnum.Tenant);
                    }}
                    sx={{
                      textTransform: 'none',
                      minWidth: '100px',
                      bgcolor:
                        selectedTab === HierarchyLevelEnum.Tenant
                          ? '#3f51b5'
                          : '#f5f5f5',
                      color:
                        selectedTab === HierarchyLevelEnum.Tenant
                          ? '#fff'
                          : '#000',
                      borderRight: '1px solid #ddd',
                      '&:hover': {
                        bgcolor:
                          selectedTab === HierarchyLevelEnum.Tenant
                            ? '#3f51b5'
                            : '#e0e0e0',
                      },
                      borderRadius: '8px 0 0 8px', // Rounded borders for Tenant (left side)
                    }}
                  />
                  <Tab
                    label={`${HierarchyLevelEnum.SiteTag}`}
                    onClick={() => handleTabChange(HierarchyLevelEnum.SiteTag)}
                    sx={{
                      textTransform: 'none',
                      minWidth: '100px',
                      bgcolor:
                        selectedTab === HierarchyLevelEnum.SiteTag
                          ? '#3f51b5'
                          : '#f5f5f5',
                      color:
                        selectedTab === HierarchyLevelEnum.SiteTag
                          ? '#fff'
                          : '#000',
                      borderRight: '1px solid #ddd',
                      '&:hover': {
                        bgcolor:
                          selectedTab === HierarchyLevelEnum.SiteTag
                            ? '#3f51b5'
                            : '#e0e0e0',
                      },
                    }}
                  />
                  <Tab
                    label={`${HierarchyLevelEnum.Site}`}
                    onClick={() => handleTabChange(HierarchyLevelEnum.Site)}
                    sx={{
                      textTransform: 'none',
                      minWidth: '100px',
                      bgcolor:
                        selectedTab === HierarchyLevelEnum.Site
                          ? '#3f51b5'
                          : '#f5f5f5',
                      color:
                        selectedTab === HierarchyLevelEnum.Site
                          ? '#fff'
                          : '#000',
                      borderRight: '1px solid #ddd',
                      '&:hover': {
                        bgcolor:
                          selectedTab === HierarchyLevelEnum.Site
                            ? '#3f51b5'
                            : '#e0e0e0',
                      },
                    }}
                  />
                  <Tab
                    label={`${HierarchyLevelEnum.Device}`}
                    onClick={() => handleTabChange(HierarchyLevelEnum.Device)}
                    sx={{
                      textTransform: 'none',
                      minWidth: '100px',
                      bgcolor:
                        selectedTab === HierarchyLevelEnum.Device
                          ? '#3f51b5'
                          : '#f5f5f5',
                      color:
                        selectedTab === HierarchyLevelEnum.Device
                          ? '#fff'
                          : '#000',
                      borderRight: '1px solid #ddd',
                      '&:hover': {
                        bgcolor:
                          selectedTab === HierarchyLevelEnum.Device
                            ? '#3f51b5'
                            : '#e0e0e0',
                      },
                      borderRadius: '0 8px 8px 0', // Rounded for Tenant (left side)
                    }}
                  />
                </Tabs>
              </Box>
            </Box>
          </Box>
        )}
        {isPlusButtonClicked && showAssignmentPage && (
          <AssignmentDialog
            data-testid='assignmentDialog'
            open={isAddToAssignmentModalOpen}
            onClose={handleClose}
            title={`${
              selectedAssignments[focusComponent]?.length || 0
            } Selected ${focusComponent} for Assignment`}
            callback={retrieveAllAssetByType}
            updateAssignments={updateAssignments(HierarchyLevelEnum?.[level])}
            instanceId={id}
            level={level}
            allAssignments={allAssignments}
            selectedAssignments={selectedAssignments}
            onDataFetched={createEntitiesList}
            isAssignmentsEmpty={isAssignmentsEmpty}
            defaultDeploymentType={
              RevisionPublishDeploymentType.maintenanceWindow
            }
            isClearingAssignments
            isLoading={false}
            onDepSubmit={handleOnSubmit}
            assignments={assignments.configAssignments}
            setIsCancelClicked={setIsCancelClicked}
            isMfaAccess={hasMfaAccess}
            clearAssignments={clearAssignments}
          />
        )}
        {isCancelClicked && (
          <ConfirmDialog
            data-testid='cancelDialog'
            open={isCancelClicked}
            onApply={() => {
              setStates({
                selectedAssignments: initAssignments,
                isReadOnly: true,
              });
              setIsCancelClicked(false);
            }}
            onCancel={() => {
              setIsCancelClicked(false);
            }}
            title='Cancel assignment changes'
            description='This action will discard any unsaved changes to the current assignment. Do you wish to continue?'
            applyButtonName='Discard unsaved changes'
          />
        )}
      </Box>
    </Drawer>
  );
};

export default Assignment;
