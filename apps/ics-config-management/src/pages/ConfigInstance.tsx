/* eslint-disable import/no-cycle */
import { Box, CircularProgress, Skeleton } from '@mui/material';
import { useSnackbar } from 'notistack';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useState,
  Suspense,
  FC,
} from 'react';
import { useParams, useNavigate } from 'react-router';
import { useQueryClient } from '@tanstack/react-query';
import { lowerCase } from 'lodash';
import ActionButton from '../components/ActionButton';
import useGlobal from '../hooks/useContext';
import { useMergeState } from '../hooks/useMergeStates';
import MainLayout from '../layouts/MainLayout';
import { getInstanceCsvUrl } from '../services/api-request';
import {
  useGetInstanceDetails,
  usePutUpdateInstance,
} from '../services/use-query';
import {
  getConfigVersion,
  hasFeatureFlag,
  hasUserRole,
} from '../utils/helpers';
import FeatureFlags from '../constants/featureFlags';
import UserRoles from '../constants/userRoles';
import { getSchemaRendererType } from '../components/DynamicEditor/utils/getSchemaRendererType';
import DynamicTitle from '../components/DynamicTitle';
import lazyWithPreload from '../utils/lazyWithPreload';
import { CONFIG_VERSION } from '../constants/staticContent';
import { TabProps, TabType } from '../constants/types';
import { CONFIG_INSTANCE_TAB_ROOT } from '../constants/routes';
import Tabs from '../constants/entities';

const Assignment = lazyWithPreload(
  () => import('./ConfigInstanceTabs/Assignment')
);
const Deployment = lazyWithPreload(
  () => import('./ConfigInstanceTabs/Deployment')
);
const RevisionComponent = lazyWithPreload(
  () => import('./ConfigInstanceTabs/Revision')
);

Assignment.preload();
Deployment.preload();
RevisionComponent.preload();

const TabComponents: { [key in Lowercase<TabType>]: FC<TabProps> } = {
  revisions: RevisionComponent,
  assignment: Assignment,
  deployment: Deployment,
};

interface State {
  isLoading?: boolean;
  title?: string;
  eyebrow?: string;
  configVersion?: string;
  canBeDeployed?: boolean;
}
const getTabContent = (tab: string) =>
  !TabComponents[tab] ? RevisionComponent : TabComponents[tab];

const ConfigInstance = () => {
  const { tab, instanceID: instanceId } = useParams();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const [{ isLoading, title, eyebrow, configVersion }, setStates] =
    useMergeState<State>({});
  const { isDeployable: canBeDeployed, updateDeployable } = useGlobal();
  const [isSchemaTable, setIsSchemaTable] = useState(false);
  const [isRenamingConfigError, setIsRenamingConfigError] = useState(false);
  const [showAssignmentDrawer, setShowAssignmentDrawer] = useState(false);
  const queryClient = useQueryClient();
  const TabContent = getTabContent(tab);

  // Handle direct navigation to assignment URL - redirect to revisions and open drawer
  useEffect(() => {
    if (tab === 'assignment') {
      navigate(`${CONFIG_INSTANCE_TAB_ROOT}/${instanceId}/revisions`, {
        replace: true,
      });
      setShowAssignmentDrawer(true);
    }
  }, [tab, instanceId, navigate]);

  const hasDeployPermission = useMemo(
    () =>
      hasFeatureFlag(FeatureFlags.CONFIG_MGMT) &&
      hasUserRole(UserRoles.CONFIG_MGMT_DEPLOY) &&
      !!instanceId,
    [instanceId]
  );

  const handleAssignmentTabClick = useCallback(() => {
    setShowAssignmentDrawer(true);
  }, []);

  const handleAssignmentDrawerClose = useCallback(() => {
    setShowAssignmentDrawer(false);
  }, []);

  const links = Object.values(Tabs).map((tabName: string) => {
    const key = lowerCase(tabName) as Lowercase<TabType>;

    // For assignment tab, use onClick handler instead of navigation
    if (key === 'assignment') {
      return {
        key,
        label: tabName,
        onClick: handleAssignmentTabClick,
      };
    }

    return {
      key,
      to: `${CONFIG_INSTANCE_TAB_ROOT}/${instanceId}/${key}`,
      label: tabName,
    };
  });

  const { data: instanceDetails, refetch: refetchInstanceDetails } =
    useGetInstanceDetails(instanceId);

  const handleCsvClick = async (id: string) => {
    await getInstanceCsvUrl(id, instanceDetails.instanceName);
  };

  const fetchInstanceDetails = useCallback(
    async (details: any) => {
      setStates({ isLoading: true });
      updateDeployable(false);

      try {
        if (details) {
          const {
            instanceName,
            appName,
            configFile,
            isDeployable,
            configurationVersion,
          } = details;
          const newEyebrow = `${appName || ''}${
            configFile ? ` ${configFile}` : ''
          }`;
          const newConfigVersion = `${CONFIG_VERSION} ${getConfigVersion(
            configurationVersion
          )}`;
          setStates({
            title: instanceName,
            eyebrow: newEyebrow,
            configVersion: newConfigVersion,
          });
          updateDeployable(isDeployable && hasDeployPermission);
        }
      } catch (e) {
        enqueueSnackbar(e.message, { variant: 'error' });
      }
      setStates({ isLoading: false });
    },
    [
      enqueueSnackbar,
      hasDeployPermission,
      instanceId,
      setStates,
      updateDeployable,
    ]
  );

  useEffect(() => {
    if (canBeDeployed === undefined) refetchInstanceDetails();
    fetchInstanceDetails(instanceDetails);
  }, [canBeDeployed, instanceDetails]);

  useEffect(() => {
    if (instanceDetails) {
      setIsSchemaTable(
        getSchemaRendererType(
          instanceDetails?.configSchemaRenderType,
          instanceDetails?.schemaType
        ) === 'table'
      );
    }
  }, [instanceDetails]);

  const { mutate: updateConfigInstance, isLoading: isUpdateInstanceLoading } =
    usePutUpdateInstance(instanceId);

  const onSaveTitle = (text: string) => {
    setStates({ title: text });

    updateConfigInstance(
      {
        instanceId,
        payload: {
          instanceName: text,
        },
      },
      {
        onSuccess: () => {
          setIsRenamingConfigError(false);
          enqueueSnackbar('Config instance updated successfully', {
            variant: 'success',
          });
          queryClient.invalidateQueries(['getInstances']);
        },
        onError: () => {
          setIsRenamingConfigError(true);
          enqueueSnackbar('Config instance update failed', {
            variant: 'error',
          });
        },
      }
    );
  };

  const dynamicTitle = (
    <DynamicTitle
      text={title}
      onSave={onSaveTitle}
      hasError={isRenamingConfigError}
      disabled={isUpdateInstanceLoading}
    />
  );

  return (
    <MainLayout
      isLoading={isLoading}
      header={{
        title,
        eyebrow,
        configVersion,
        links,
        dynamicTitle,
        activeTab: showAssignmentDrawer ? 'assignment' : undefined,
        action: (
          <Box alignSelf='flex-start'>
            <ActionButton
              instanceId={instanceId}
              canExportConfig={isSchemaTable}
              handleCsvClick={handleCsvClick}
            />
          </Box>
        ),
      }}
    >
      {isLoading && !instanceId ? (
        <Box
          width='100%'
          height='100%'
          display='flex'
          justifyContent='center'
          alignItems='center'
        >
          <CircularProgress />
        </Box>
      ) : (
        <Suspense
          fallback={
            <Box height='100%' p={2} bgcolor='common.backgroundLight'>
              <Box bgcolor='common.white' p='24px' borderRadius='16px'>
                <Box width='100%' maxWidth='800px' gap={3} display='grid'>
                  <Skeleton width='100%' height='32px' />
                  <Skeleton width='100%' height='32px' />
                  <Skeleton width='40%' height='32px' />
                </Box>
              </Box>
            </Box>
          }
        >
          {/* Only render non-assignment tabs through routing */}
          {tab !== 'assignment' && (
            <TabContent
              id={instanceId}
              isDeployable={instanceDetails?.isDeployable}
              instanceDetails={instanceDetails}
            />
          )}

          {/* Render assignment drawer separately */}
          {showAssignmentDrawer && (
            <Assignment
              id={instanceId}
              instanceDetails={instanceDetails}
              isVisible={showAssignmentDrawer}
              onClose={handleAssignmentDrawerClose}
            />
          )}
        </Suspense>
      )}
    </MainLayout>
  );
};

export default ConfigInstance;
