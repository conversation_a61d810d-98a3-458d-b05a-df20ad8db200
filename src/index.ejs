<!DOCTYPE html>
<html
  lang="en"
  class="{{$state.current.data.viewClass}}"
  data-ics-prevent-drop data-ng-controller="AppCtrl"
  data-ng-class="{'global-menu-open' : isGlobalMenuOpen}"
>
  <head>

  <meta charset="UTF-8">

  <meta name="viewport" content="width=device-width, initial-scale=1.0 ,maximum-scale=1, user-scalable=no">

  <meta http-equiv="X-UA-Compatible" content="ie=edge">

  <meta http-equiv="Access-Control-Allow-Origin" content="*">

  <meta name="importmap-type" content="systemjs-importmap" />

  <title>Invenco Cloud Services</title>
  <title ng-bind="$state.current.data.titleTag + ' | ' + adminInstance.productName"></title>

  <base href="/">

  <link rel="dns-prefetch" href="//s3.amazonaws.com">

  <link rel="dns-prefetch" href="//maps.googleapis.com">

  <link rel="shortcut icon" href="assets/img/favicon/favicon.ico">
  
  <link rel="preconnect" href="https://fonts.gstatic.com">

  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet">

  <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">

  <link rel="stylesheet" href="common/ics-styles/css/webfonts.css">

  <link rel="stylesheet" href="assets/css/<%= tms.css('ics-bundle') %>">
  
  <!-- FROM ICS-NEXT-V2 -->
  <script>window.ngDevMode = false;</script>

  <!--
    Remove this if you only support browsers that support async/await.
    This is needed by babel to share largeish helper code for compiling async/await in older
    browsers. More information at https://github.com/single-spa/create-single-spa/issues/112
  -->
  <script src="https://cdn.jsdelivr.net/npm/regenerator-runtime@0.13.5/runtime.min.js" integrity="sha512-i59I9RzmTYuKgAaGn6Sp9jsA/yIeZdWYctJBIeBV/7ezJZa7H4n98s9QyAhlSUKvLSylFECAYVUeYKx4MNtrSA==" crossorigin="anonymous"></script>

  <!-- Add your organization's prod import map URL to this script's src  -->
  <script type="systemjs-importmap" src="/importmap.json"></script>
  
  <script src="https://cdnjs.cloudflare.com/ajax/libs/systemjs/6.4.0/system.min.js" integrity="sha512-px4Ekhi/dpQqgX6pkWO3umIzSNU8B5Y5uNhMYIxG2X8E9BjGRTVRY/jQr4ffWOKPorsGN1m6xK0Yd+ob+2KIiQ==" crossorigin="anonymous"></script>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/systemjs/6.4.0/extras/amd.min.js" integrity="sha512-QiNzAj0YlaEXlnd83IcIdlgQ5SsBmOjh7zQlR7gPXJl4DrMmtDKT5UiT+O7Yv+gwGxRcAOEfTgo4yHBDshxplg==" crossorigin="anonymous"></script>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/systemjs/6.4.0/extras/named-register.min.js" integrity="sha512-XRrZRDEJK7FeoBd7ICImm9S3UNG87nFAUm4td9v2uedBr16byMk9FK4AZ4N608aVHHSKPlhdSWTy/b717KJ54Q==" crossorigin="anonymous"></script>
  
  <script src="https://cdnjs.cloudflare.com/ajax/libs/systemjs/6.4.0/extras/named-exports.min.js" integrity="sha512-mf4/y3IroNfkl20Brygdc+tb4a38MrEmU+VTSyMaqlsZhfVHCdHniA//HEgETGlJptSSCPv72Dy6kXB+OHiGAQ==" crossorigin="anonymous"></script>
  
  <script src="https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.5.0/sql-wasm.min.js" integrity="sha512-l5XgljO54rARJoeqQoY4w0sAJVFd/0GVSvFNtr9YSCSEe+M7Lg0tDw7WQg1J06Mr0/2f9M6ExdHBChwxWammKA==" crossorigin="anonymous"></script>
  
  <!--
    This CSP allows any SSL-enabled host, but you should limit these directives further to increase your app's security.
    Learn more about CSP policies at https://content-security-policy.com/#directive
  -->
  <!-- <meta http-equiv="Content-Security-Policy" content="default-src 'self' https: localhost:*; script-src 'unsafe-inline' https: localhost:*; connect-src https: localhost:* ws://localhost:*; style-src 'unsafe-inline' https:; object-src 'none';"> -->
  
  
  <!-- If you wish to turn off import-map-overrides for specific environments (prod), uncomment the line below -->
  <!-- More info at https://github.com/joeldenning/import-map-overrides/blob/master/docs/configuration.md#domain-list -->
  <!-- <meta name="import-map-overrides-domains" content="denylist:prod.example.com" /> -->

  <!-- Shared dependencies go into this import map. Your shared dependencies must be of one of the following formats:

    1. System.register (preferred when possible) - https://github.com/systemjs/systemjs/blob/master/docs/system-register.md
    2. UMD - https://github.com/umdjs/umd
    3. Global variable

    More information about shared dependencies can be found at https://single-spa.js.org/docs/recommended-setup#sharing-with-import-maps.
  -->
  <!-- <link rel="preload" crossorigin href="https://cdn.jsdelivr.net/npm/single-spa@5.5.5/lib/system/single-spa.min.js" as="script"> -->

  <script id="newRelic"></script>

  <script src="assets/js/<%= tms.scripts('modernizr-ics') %>"></script>

  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/import-map-overrides/dist/import-map-overrides.js"></script>
  
</head>

<body dir="ltr" ng-csp="no-unsafe-eval">

  <div id="auth-app"></div>

  <div id="layout-app"></div>

  <div id="main-app" class="mfe-container col-main"></div>
  <div id="ics-tms-app-v2" class="mfe-container"></div>

  <!-- START: TMS-WEB LEGACY TAG -->
  <div class="ng-cloak" data-ng-cloak data-ui-view></div>

  <div data-ng-if="::false">
    <div class="ics-file-uploader-backdrop">
      <div class="ics-file-uploader">
        <svg xmlns="http://www.w3.org/2000/svg" viewbox="25 25 50 50" aria-labelledby="indexLoading" role="img">
          <title id="indexLoading">Loading</title>
          <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="5" stroke-miterlimit="10" />
        </svg>
      </div>
    </div>
  </div>

  <!-- PLACEHOLDER FOR ngModal USING BOOTSTRAP V5.3.3 -->
  <div id="ng-modal-container" class="bootstrap-iso"></div>

  <!-- END: TMS-WEB LEGACY TAG -->

  <!--[if lt IE 10]><iframe title="Update Your Browser" class="browserFrame" src="notsupported.html"></iframe><![endif]-->
  <script src="assets/js/<%= tms.scripts('vendor') %>"></script>
  <script defer src="assets/js/<%= tms.scripts('ics-core') %>"></script>

  <script>
    System.import('@ics-next/root-config');
    System.import('@ics/dashboard');
    System.import('@ics/asset-management');
    System.import('@ics/config-management');
    System.import('@ics/fuel-price-management');
    System.import('@ics/playlist');
    System.import('@ics/report-management');
  </script>
</body>

  <footer>
    <a href="https://vontier692128.app.privacycenter.cloud/#/?tab=HOME" />
  </footer>

</html>
